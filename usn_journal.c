#include "usn_journal.h"
#include "file_watcher.h"

// ============================================================================
// 全局变量和常量定义
// ============================================================================
static BOOL g_processing = FALSE;

// ============================================================================
// 核心USN Journal扫描功能
// ============================================================================

// ============================================================================
// 多线程USN扫描实现
// ============================================================================

// 兼容性函数
int ReadUSNToMemory()
{
    return ReadUSNToMemoryMultiThreaded();
}

// 兼容性函数
int ScanAllDrives()
{
    return ReadUSNToMemoryMultiThreaded();
}

// ============================================================================
// 简化的目录扫描函数（回退方案）
// ============================================================================

// 简化的目录扫描（当USN Journal不可用时的回退方案）
int ScanDirectoryWithDepth(const WCHAR *dirPath, int *totalCount, int maxFiles, int currentDepth, int maxDepth)
{
    WIN32_FIND_DATAW findData;
    HANDLE hFind;
    WCHAR searchPath[MAX_PATH * 2];

    if (currentDepth > maxDepth || *totalCount >= maxFiles)
        return 0;

    swprintf(searchPath, MAX_PATH * 2,
             (wcslen(dirPath) > 0 && dirPath[wcslen(dirPath) - 1] == L'\\') ? L"%s*" : L"%s\\*",
             dirPath);

    hFind = FindFirstFileW(searchPath, &findData);
    if (hFind == INVALID_HANDLE_VALUE)
        return 0;

    do
    {
        if (wcscmp(findData.cFileName, L".") == 0 || wcscmp(findData.cFileName, L"..") == 0)
            continue;

        if (*totalCount >= maxFiles)
            break;

        WCHAR fullPath[MAX_PATH * 2];
        swprintf(fullPath, MAX_PATH * 2,
                 (wcslen(dirPath) > 0 && dirPath[wcslen(dirPath) - 1] == L'\\') ? L"%s%s" : L"%s\\%s",
                 dirPath, findData.cFileName);

        LONGLONG fileSize = ((LONGLONG)findData.nFileSizeHigh << 32) + findData.nFileSizeLow;
        AddUSNFile(findData.cFileName, fullPath, fileSize, findData.ftLastWriteTime, findData.dwFileAttributes);
        (*totalCount)++;

        if ((findData.dwFileAttributes & FILE_ATTRIBUTE_DIRECTORY) &&
            wcslen(fullPath) < (MAX_PATH - 50) && currentDepth < maxDepth)
        {
            ScanDirectoryWithDepth(fullPath, totalCount, maxFiles, currentDepth + 1, maxDepth);
        }

    } while (FindNextFileW(hFind, &findData) && *totalCount < maxFiles);

    FindClose(hFind);
    return 0;
}

// 简化的递归扫描入口
int ScanDirectoryRecursiveNew(const WCHAR *dirPath, int *totalCount, int maxFiles)
{
    return ScanDirectoryWithDepth(dirPath, totalCount, maxFiles, 0, 16);
}

int ReadUSNToMemoryMultiThreaded()
{

    DWORD drives = GetLogicalDrives();
    DriveThreadData driveData[MAX_DRIVES];
    HANDLE threads[MAX_DRIVES];
    int driveCount = 0;
    int totalCount = 0;
    DWORD startTime = GetTickCount();

    for (int i = 0; i < 26; i++)
    {
        if (drives & (1 << i))
        {
            WCHAR driveLetter = L'A' + i;
            WCHAR drivePath[4] = {driveLetter, L':', L'\\', L'\0'};

            // 检查驱动器类型
            UINT driveType = GetDriveTypeW(drivePath);
            if (driveType == DRIVE_FIXED && driveCount < MAX_DRIVES)
            {
                wcscpy(driveData[driveCount].drivePath, drivePath);
                driveData[driveCount].threadId = driveCount;
                driveData[driveCount].fileCount = 0;
                driveData[driveCount].scanTime = 0;
                driveData[driveCount].success = FALSE;
                driveCount++;
            }
        }
    }

    for (int i = 0; i < driveCount; i++)
    {
        threads[i] = CreateThread(NULL, 0, DriveThreadProc, &driveData[i], 0, NULL);
        if (threads[i] == NULL)
        {
        }
        else
        {
        }
    }

    // 等待所有线程完成
    WaitForMultipleObjects(driveCount, threads, TRUE, INFINITE);

    // 收集结果
    for (int i = 0; i < driveCount; i++)
    {
        if (driveData[i].success)
            totalCount += driveData[i].fileCount;

        if (threads[i])
            CloseHandle(threads[i]);
    }

    return 0;
}

// 驱动器扫描线程函数
DWORD WINAPI DriveThreadProc(LPVOID lpParam)
{
    DriveThreadData *data = (DriveThreadData *)lpParam;
    DWORD startTime = GetTickCount();

    int fileCount = ScanDriveUSN(data->drivePath);

    data->scanTime = GetTickCount() - startTime;
    data->fileCount = fileCount;
    data->success = (fileCount > 0);

    if (!data->success)
    {
        // 回退到递归文件扫描
        int fallbackCount = FallbackDirectoryScan(data->drivePath);
        if (fallbackCount > 0)
        {
            data->fileCount = fallbackCount;
            data->success = TRUE;
            data->scanTime = GetTickCount() - startTime;
        }
    }

    return 0;
}

// 扫描单个驱动器的USN Journal
int ScanDriveUSN(const WCHAR *drivePath)
{
    HANDLE hVolume;
    WCHAR volumePath[MAX_PATH];

    // 构建卷路径 \\.\C:
    swprintf(volumePath, MAX_PATH, L"\\\\.\\%c:", drivePath[0]);

    // 打开卷
    hVolume = CreateFileW(volumePath,
                          GENERIC_READ,
                          FILE_SHARE_READ | FILE_SHARE_WRITE,
                          NULL,
                          OPEN_EXISTING,
                          0,
                          NULL);

    if (hVolume == INVALID_HANDLE_VALUE)
        return 0;

    // 查询USN Journal信息
    USN_JOURNAL_DATA journalData;
    DWORD bytesReturned;

    if (!DeviceIoControl(hVolume,
                         FSCTL_QUERY_USN_JOURNAL,
                         NULL, 0,
                         &journalData, sizeof(journalData),
                         &bytesReturned,
                         NULL))
    {
        CloseHandle(hVolume);
        return 0;
    }

    // 枚举USN记录
    int fileCount = EnumerateUSNRecords(hVolume, drivePath);

    CloseHandle(hVolume);
    return fileCount;
}

// 优化的USN记录枚举函数
int EnumerateUSNRecords(HANDLE hVolume, const WCHAR *drivePath)
{
    // 使用动态分配的大缓冲区
    BYTE *buffer = (BYTE *)malloc(USN_BUFFER_SIZE);
    if (!buffer)
        return 0;

    MFT_ENUM_DATA mftEnumData = {0};
    DWORD bytesReturned;
    int fileCount = 0;
    int batchCount = 0;
    DWORD startTime = GetTickCount();

    // 设置枚举参数
    mftEnumData.StartFileReferenceNumber = 0;
    mftEnumData.LowUsn = 0;
    mftEnumData.HighUsn = MAXLONGLONG;

    while (TRUE)
    {
        // 枚举USN记录
        if (!DeviceIoControl(hVolume,
                             FSCTL_ENUM_USN_DATA,
                             &mftEnumData, sizeof(mftEnumData),
                             buffer, USN_BUFFER_SIZE,
                             &bytesReturned,
                             NULL))
        {
            DWORD error = GetLastError();
            if (error == ERROR_HANDLE_EOF)
            {
                printf("USN记录枚举完成\n");
                break;
            }
            else
            {
                printf("枚举USN记录失败，错误代码: %lu\n", error);
                break;
            }
        }

        if (bytesReturned < sizeof(USN))
        {
            printf("没有更多USN记录\n");
            break;
        }

        // 批量处理USN记录
        int batchProcessed = ProcessUSNBatch(buffer, bytesReturned, drivePath, &mftEnumData);
        fileCount += batchProcessed;
        batchCount++;

        // 限制文件数量避免内存溢出
        if (fileCount >= 4069000)
            break;
    }

    free(buffer);
    return fileCount;
}

// 批量处理USN记录
int ProcessUSNBatch(BYTE *buffer, DWORD bytesReturned, const WCHAR *drivePath, MFT_ENUM_DATA *mftEnumData)
{
    int processedCount = 0;
    PUSN_RECORD_V2 usnRecord;
    DWORD offset = sizeof(USN);

    while (offset < bytesReturned)
    {
        usnRecord = (PUSN_RECORD_V2)(buffer + offset);

        if (usnRecord->RecordLength == 0 || usnRecord->RecordLength > (bytesReturned - offset))
            break;

        // 快速处理USN记录
        ProcessUSNRecordFast(usnRecord, drivePath);
        processedCount++;

        // 更新下一个记录的起始位置
        mftEnumData->StartFileReferenceNumber = usnRecord->FileReferenceNumber;

        // 移动到下一个记录
        offset += usnRecord->RecordLength;
    }

    return processedCount;
}

// 优化的USN记录处理函数
void ProcessUSNRecordFast(PUSN_RECORD_V2 usnRecord, const WCHAR *drivePath)
{
    // 跳过目录和系统文件，只处理普通文件
    if (usnRecord->FileAttributes & FILE_ATTRIBUTE_DIRECTORY)
        return;

    if (usnRecord->FileAttributes & (FILE_ATTRIBUTE_SYSTEM | FILE_ATTRIBUTE_HIDDEN))
        return;

    // 快速提取文件名（避免不必要的复制）
    WCHAR *fileName = (WCHAR *)((BYTE *)usnRecord + usnRecord->FileNameOffset);
    int fileNameLength = usnRecord->FileNameLength / sizeof(WCHAR);

    // 跳过临时文件和系统文件
    if (fileNameLength > 4)
    {
        WCHAR *ext = fileName + fileNameLength - 4;
        if (wcsncmp(ext, L".tmp", 4) == 0 || wcsncmp(ext, L".log", 4) == 0)
            return;
    }

    // 构建完整路径（优化：减少字符串操作）
    WCHAR fullPath[MAX_PATH];
    swprintf(fullPath, MAX_PATH, L"%ls%.*ls", drivePath, fileNameLength, fileName);

    // 获取文件大小（USN记录中没有直接的文件大小信息，设为0）
    LONGLONG fileSize = 0;

    // 转换时间戳
    FILETIME lastWriteTime;
    lastWriteTime.dwLowDateTime = usnRecord->TimeStamp.LowPart;
    lastWriteTime.dwHighDateTime = usnRecord->TimeStamp.HighPart;

    // 添加到内存数组
    AddUSNFile(fileName, fullPath, fileSize, lastWriteTime, usnRecord->FileAttributes);
}

// 处理单个USN记录（保留原函数用于兼容性）
void ProcessUSNRecord(PUSN_RECORD_V2 usnRecord, const WCHAR *drivePath)
{
    // 获取文件名
    WCHAR fileName[MAX_PATH];
    int fileNameLength = usnRecord->FileNameLength / sizeof(WCHAR);

    if (fileNameLength >= MAX_PATH)
        fileNameLength = MAX_PATH - 1;

    wcsncpy(fileName, usnRecord->FileName, fileNameLength);
    fileName[fileNameLength] = L'\0';

    // 构建完整路径（简化版本，不重建完整路径树）
    WCHAR fullPath[MAX_PATH * 2];
    swprintf(fullPath, MAX_PATH * 2, L"%s%s", drivePath, fileName);

    // 获取文件大小（USN记录中没有直接的文件大小信息，设为0）
    LONGLONG fileSize = 0;

    // 使用当前时间作为修改时间（USN记录中有时间戳，但这里简化处理）
    FILETIME currentTime;
    GetSystemTimeAsFileTime(&currentTime);

    // 添加到内存 (USN记录中没有文件属性，假设为文件)
    AddUSNFile(fileName, fullPath, fileSize, currentTime, 0);
}

// 回退的普通目录扫描 - 优化版本，避免卡死
int FallbackDirectoryScan(const WCHAR *drivePath)
{

    int totalCount = 0;
    int maxFiles = 4069000; // 设置为406.9万个文件

    // 使用带深度控制的递归扫描函数
    ScanDirectoryRecursiveNew(drivePath, &totalCount, maxFiles);

    printf("优化目录扫描完成，共 %d 个文件\n", totalCount);
    return totalCount;
}

// 简单的非递归扫描 - 作为最后的备用方案
int SimpleDirectoryScan(const WCHAR *drivePath)
{
    printf("开始简单目录扫描: %ls (扫描根目录所有文件和文件夹)\n", drivePath);

    int totalCount = 0;

    // 先扫描根目录
    WIN32_FIND_DATAW findData;
    HANDLE hFind;
    WCHAR searchPath[MAX_PATH];

    swprintf(searchPath, MAX_PATH, L"%s*", drivePath);
    hFind = FindFirstFileW(searchPath, &findData);

    if (hFind != INVALID_HANDLE_VALUE)
    {
        do
        {
            if (wcscmp(findData.cFileName, L".") != 0 && wcscmp(findData.cFileName, L"..") != 0)
            {
                WCHAR fullPath[MAX_PATH * 2];
                swprintf(fullPath, MAX_PATH * 2, L"%s%s", drivePath, findData.cFileName);

                LONGLONG fileSize = ((LONGLONG)findData.nFileSizeHigh << 32) + findData.nFileSizeLow;
                AddUSNFile(findData.cFileName, fullPath, fileSize, findData.ftLastWriteTime, findData.dwFileAttributes);
                totalCount++;

                if (totalCount >= 4069000)
                    break;
            }
        } while (FindNextFileW(hFind, &findData));

        FindClose(hFind);
    }

    printf("简单目录扫描完成，共 %d 个文件\n", totalCount);
    return totalCount;
}

// Initialize SQLite database (disabled - using USN memory only)
int InitializeDatabase(const char *dbPath)
{
    printf("Database functionality disabled - using USN memory only\n");
    return 0; // Return success
}

// Insert file record into database (optimized version)
int InsertFileRecord(const char *name, const char *path, LONGLONG size, const char *modified)
{
    // Database functionality disabled - using USN memory only
    return 0; // Return success
}

// Update file record in database (disabled)
int UpdateFileRecord(const char *path, LONGLONG size, const char *modified)
{
    return 0; // Return success
}

// Delete file record from database (disabled)
int DeleteFileRecord(const char *path)
{
    return 0; // Return success
}

// Clear all records from database (disabled)
int ClearDatabase()
{
    return 0; // Return success
}

// Close database (disabled)
void CloseDatabase()
{
    printf("Database functionality disabled\n");
}

// Compress database manually (disabled)
int CompressDatabase()
{
    printf("Database compression disabled\n");
    return 0; // Return success
}

// Open volume handle
BOOL OpenVolume(VolumeProcessor *processor, WCHAR driveLetter)
{
    WCHAR volumePath[10];
    swprintf(volumePath, 10, L"\\\\.\\%c:", driveLetter);

    printf("Attempting to open volume: %ls\n", volumePath);

    // First try with maximum privileges
    processor->volumeHandle = CreateFileW(
        volumePath,
        GENERIC_READ | GENERIC_WRITE,
        FILE_SHARE_READ | FILE_SHARE_WRITE,
        NULL,
        OPEN_EXISTING,
        FILE_ATTRIBUTE_READONLY,
        NULL);

    if (processor->volumeHandle == INVALID_HANDLE_VALUE)
    {
        DWORD error = GetLastError();
        printf("First attempt failed. Error %lu. Trying with reduced privileges...\n", error);

        // Try with read-only access
        processor->volumeHandle = CreateFileW(
            volumePath,
            GENERIC_READ,
            FILE_SHARE_READ | FILE_SHARE_WRITE,
            NULL,
            OPEN_EXISTING,
            0,
            NULL);
    }

    if (processor->volumeHandle == INVALID_HANDLE_VALUE)
    {
        DWORD error = GetLastError();
        printf("Failed to open volume %c: Error %lu", (char)driveLetter, error);

        switch (error)
        {
        case ERROR_ACCESS_DENIED:
            printf(" (Access Denied - Administrator privileges required)\n");
            printf("TROUBLESHOOTING:\n");
            printf("1. Ensure you're running as Administrator\n");
            printf("2. Check if antivirus is blocking access\n");
            printf("3. Verify USN Journal is enabled: fsutil usn queryjournal %c:\n", (char)driveLetter);
            printf("4. Try: fsutil usn createjournal m=1000 a=100 %c:\n", (char)driveLetter);
            break;
        case ERROR_FILE_NOT_FOUND:
            printf(" (Drive not found)\n");
            break;
        case ERROR_INVALID_DRIVE:
            printf(" (Invalid drive)\n");
            break;
        default:
            printf(" (Unknown error)\n");
            break;
        }
        return FALSE;
    }

    processor->volumeLetter = driveLetter;

    // Initialize FRN map
    processor->frnMapCapacity = 10000;
    processor->frnMapSize = 0;
    processor->frnMap = (FrnEntry *)malloc(processor->frnMapCapacity * sizeof(FrnEntry));

    return TRUE;
}

// Create USN Journal
BOOL CreateUSNJournal(VolumeProcessor *processor)
{
    processor->createData.MaximumSize = 0;     // Use default
    processor->createData.AllocationDelta = 0; // Use default

    DWORD bytesReturned;
    BOOL result = DeviceIoControl(
        processor->volumeHandle,
        FSCTL_CREATE_USN_JOURNAL,
        &processor->createData,
        sizeof(processor->createData),
        NULL,
        0,
        &bytesReturned,
        NULL);

    if (!result)
    {
        printf("Failed to create USN journal: Error %lu\n", GetLastError());
    }

    return result;
}

// Query USN Journal information
BOOL QueryUSNJournal(VolumeProcessor *processor)
{
    DWORD bytesReturned;
    BOOL result = DeviceIoControl(
        processor->volumeHandle,
        FSCTL_QUERY_USN_JOURNAL,
        NULL,
        0,
        &processor->journalData,
        sizeof(processor->journalData),
        &bytesReturned,
        NULL);

    if (!result)
    {
        printf("Failed to query USN journal: Error %lu\n", GetLastError());
        return result;
    }

    // 初始化实时监控字段
    processor->nextUsn = processor->journalData.NextUsn;
    processor->journalId = processor->journalData.UsnJournalID;

    printf("USN Journal initialized: NextUSN=%lld, JournalID=%lld\n",
           processor->nextUsn, processor->journalId);

    return result;
}

// Add FRN entry to map
void AddFrnEntry(VolumeProcessor *processor, DWORDLONG frn, DWORDLONG parentFrn, const WCHAR *fileName)
{
    if (processor->frnMapSize >= processor->frnMapCapacity)
    {
        processor->frnMapCapacity *= 2;
        processor->frnMap = (FrnEntry *)realloc(processor->frnMap,
                                                processor->frnMapCapacity * sizeof(FrnEntry));
    }

    FrnEntry *entry = &processor->frnMap[processor->frnMapSize++];
    entry->frn = frn;
    entry->parentFrn = parentFrn;
    wcsncpy(entry->fileName, fileName, MAX_PATH - 1);
    entry->fileName[MAX_PATH - 1] = L'\0';
}

// Find FRN entry in map
FrnEntry *FindFrnEntry(VolumeProcessor *processor, DWORDLONG frn)
{
    for (DWORD i = 0; i < processor->frnMapSize; i++)
    {
        if (processor->frnMap[i].frn == frn)
        {
            return &processor->frnMap[i];
        }
    }
    return NULL;
}

// Get full path from FRN
BOOL GetFullPath(VolumeProcessor *processor, DWORDLONG frn, WCHAR *fullPath, DWORD pathSize)
{
    WCHAR tempPath[MAX_PATH_LENGTH] = L"";
    DWORDLONG currentFrn = frn;

    // Build path backwards
    while (currentFrn != 0)
    {
        FrnEntry *entry = FindFrnEntry(processor, currentFrn);
        if (!entry)
        {
            break;
        }

        // Prepend current name to path
        WCHAR newPath[MAX_PATH_LENGTH];
        if (wcslen(tempPath) > 0)
        {
            swprintf(newPath, MAX_PATH_LENGTH, L"%s\\%s", entry->fileName, tempPath);
        }
        else
        {
            wcscpy(newPath, entry->fileName);
        }
        wcscpy(tempPath, newPath);

        currentFrn = entry->parentFrn;

        // Root directory check
        if (currentFrn == 0x20000000000005ULL)
        {
            break;
        }
    }

    // Add drive letter
    swprintf(fullPath, pathSize, L"%c:\\%s", (char)processor->volumeLetter, tempPath);
    return TRUE;
}

// Convert file time to string
void FileTimeToString(const LARGE_INTEGER *fileTime, char *timeString, size_t bufferSize)
{
    FILETIME ft;
    SYSTEMTIME st;

    ft.dwLowDateTime = fileTime->LowPart;
    ft.dwHighDateTime = fileTime->HighPart;

    if (FileTimeToSystemTime(&ft, &st))
    {
        snprintf(timeString, bufferSize, "%04d-%02d-%02d %02d:%02d:%02d",
                 st.wYear, st.wMonth, st.wDay, st.wHour, st.wMinute, st.wSecond);
    }
    else
    {
        strcpy(timeString, "Unknown");
    }
}

// Convert file size to LONGLONG
LONGLONG FileSizeToLongLong(const LARGE_INTEGER *fileSize)
{
    return fileSize->QuadPart;
}

// Convert WCHAR to char with proper UTF-8 handling
void WCharToChar(const WCHAR *wstr, char *str, size_t bufferSize)
{
    if (!wstr || !str || bufferSize == 0)
    {
        if (str && bufferSize > 0)
            str[0] = '\0';
        return;
    }

    // 使用UTF-8编码存储到数据库，确保中文字符正确保存
    int result = WideCharToMultiByte(CP_UTF8, 0, wstr, -1, str, (int)bufferSize, NULL, NULL);
    if (result == 0)
    {
        // 如果UTF-8转换失败，尝试使用系统默认编码
        result = WideCharToMultiByte(CP_ACP, 0, wstr, -1, str, (int)bufferSize, NULL, NULL);
        if (result == 0)
        {
            // 如果转换失败，清空字符串
            if (str && bufferSize > 0)
                str[0] = '\0';
        }
    }

    // 确保字符串以null结尾
    str[bufferSize - 1] = '\0';
}

// Enumerate USN Data - Core function
BOOL EnumerateUSNData(VolumeProcessor *processor)
{
    MFT_ENUM_DATA enumData;
    enumData.StartFileReferenceNumber = 0;
    // 修改：扫描整个USN日志范围，而不仅仅是最近的变化
    enumData.LowUsn = 0; // 从头开始扫描
    enumData.HighUsn = processor->journalData.NextUsn;

    // Add root directory entry
    WCHAR rootName[10];
    swprintf(rootName, 10, L"%c:", (char)processor->volumeLetter);
    AddFrnEntry(processor, 0x20000000000005ULL, 0, rootName);

    CHAR buffer[USN_BUFFER_SIZE];
    DWORD bytesReturned;
    DWORD totalFiles = 0;

    printf("Enumerating files on drive %c:...\n", (char)processor->volumeLetter);

    while (DeviceIoControl(
        processor->volumeHandle,
        FSCTL_ENUM_USN_DATA,
        &enumData,
        sizeof(enumData),
        buffer,
        USN_BUFFER_SIZE,
        &bytesReturned,
        NULL))
    {
        DWORD dwRetBytes = bytesReturned - sizeof(USN);
        PUSN_RECORD_V2 usnRecord = (PUSN_RECORD_V2)((PCHAR)buffer + sizeof(USN));

        while (dwRetBytes > 0)
        {
            // Extract file name
            WCHAR fileName[MAX_PATH];
            DWORD nameLength = usnRecord->FileNameLength / sizeof(WCHAR);
            if (nameLength >= MAX_PATH)
                nameLength = MAX_PATH - 1;

            wcsncpy(fileName, usnRecord->FileName, nameLength);
            fileName[nameLength] = L'\0';

            // 检查是否包含中文
            BOOL hasChinese = FALSE;
            for (DWORD i = 0; i < nameLength; i++)
            {
                if (fileName[i] > 127)
                {
                    hasChinese = TRUE;
                    break;
                }
            }
            if (hasChinese)
            {
                wprintf(L"[USN] 发现中文文件名: %ls\n", fileName);
            }

            // Add to FRN map
            AddFrnEntry(processor,
                        usnRecord->FileReferenceNumber,
                        usnRecord->ParentFileReferenceNumber,
                        fileName);

            totalFiles++;
            if (totalFiles % 1000 == 0)
            {
                printf("Processed %lu files...\n", totalFiles);
            }

            // Move to next record
            DWORD recordLength = usnRecord->RecordLength;
            dwRetBytes -= recordLength;
            usnRecord = (PUSN_RECORD_V2)((PCHAR)usnRecord + recordLength);
        }

        // Get next page
        enumData.StartFileReferenceNumber = *(USN *)&buffer;
    }

    printf("Total files found: %lu\n", totalFiles);
    return TRUE;
}

// Process files and insert into database
BOOL ProcessFilesToDatabase(VolumeProcessor *processor)
{
    printf("Processing files to database...\n");
    DWORD processedCount = 0;
    DWORD chineseFileCount = 0;

    for (DWORD i = 0; i < processor->frnMapSize; i++)
    {
        FrnEntry *entry = &processor->frnMap[i];

        // Skip root directory
        if (entry->frn == 0x20000000000005ULL)
        {
            continue;
        }

        // Get full path
        WCHAR fullPathW[MAX_PATH_LENGTH];
        if (!GetFullPath(processor, entry->frn, fullPathW, MAX_PATH_LENGTH))
        {
            wprintf(L"[USN] 拼接路径失败: 文件名=%ls, FRN=%llu, 父FRN=%llu\n", entry->fileName, entry->frn, entry->parentFrn);
            continue;
        }

        // Convert to UTF-8
        char fullPath[MAX_PATH_LENGTH];
        char fileName[MAX_PATH];
        WCharToChar(fullPathW, fullPath, sizeof(fullPath));
        WCharToChar(entry->fileName, fileName, sizeof(fileName));

        // 检查是否包含中文字符 (在Unicode字符串上检测)
        BOOL hasChinese = FALSE;
        int pathLen = wcslen(fullPathW);
        for (int j = 0; j < pathLen; j++)
        {
            if (fullPathW[j] > 127)
            {
                hasChinese = TRUE;
                break;
            }
        }

        if (hasChinese)
        {
            chineseFileCount++;
            // 调试输出中文路径
            if (chineseFileCount <= 10) // 只输出前10个中文路径
            {
                printf("Chinese path found: %s (FRN: %llu)\n", fullPath, entry->frn);
                printf("  Filename: %s\n", fileName);
            }
        }

        // 获取实际文件信息
        LONGLONG fileSize = 0;
        char modifiedTime[64] = "Unknown";

        // 尝试获取文件属性
        WIN32_FILE_ATTRIBUTE_DATA fileData;
        if (GetFileAttributesExW(fullPathW, GetFileExInfoStandard, &fileData))
        {
            // 获取文件大小
            LARGE_INTEGER size;
            size.LowPart = fileData.nFileSizeLow;
            size.HighPart = fileData.nFileSizeHigh;
            fileSize = size.QuadPart;

            // 转换修改时间
            ConvertFileTimeToString(&fileData.ftLastWriteTime, modifiedTime, sizeof(modifiedTime));
        }
        else
        {
            // 如果无法获取文件属性，记录错误但继续处理
            if (hasChinese)
            {
                printf("Failed to get attributes for Chinese path: %s (Error: %lu)\n", fullPath, GetLastError());
            }

            // 使用当前时间
            SYSTEMTIME st;
            GetSystemTime(&st);
            snprintf(modifiedTime, sizeof(modifiedTime), "%04d-%02d-%02d %02d:%02d:%02d",
                     st.wYear, st.wMonth, st.wDay, st.wHour, st.wMinute, st.wSecond);
        }

        // Insert into database
        if (hasChinese)
        {
            wprintf(L"[USN] 即将写入数据库: 文件名=%ls, 路径=%ls\n", entry->fileName, fullPathW);
        }
        if (InsertFileRecord(fileName, fullPath, fileSize, modifiedTime) == 0)
        {
            processedCount++;
        }

        // 每1000个文件输出进度，每5000个文件触发一次文件树更新
        if (processedCount % 1000 == 0)
        {
            printf("Inserted %lu records into database...\n", processedCount);
        }

        if (processedCount % 5000 == 0)
        {
            printf("Triggering file tree update after %lu files...\n", processedCount);
            // 这里可以添加通知主线程更新文件树的逻辑
            // 由于我们在实时渲染线程中定期检查数据库，这里不需要额外操作
        }
    }

    printf("Successfully processed %lu files to database\n", processedCount);
    printf("Found %lu files with Chinese characters\n", chineseFileCount);
    return TRUE;
}

// Delete USN Journal
BOOL DeleteUSNJournal(VolumeProcessor *processor)
{
    processor->deleteData.UsnJournalID = processor->journalData.UsnJournalID;
    processor->deleteData.DeleteFlags = USN_DELETE_FLAG_DELETE;

    DWORD bytesReturned;
    BOOL result = DeviceIoControl(
        processor->volumeHandle,
        FSCTL_DELETE_USN_JOURNAL,
        &processor->deleteData,
        sizeof(processor->deleteData),
        NULL,
        0,
        &bytesReturned,
        NULL);

    if (!result)
    {
        printf("Failed to delete USN journal: Error %lu\n", GetLastError());
    }

    return result;
}

// Close volume
void CloseVolume(VolumeProcessor *processor)
{
    if (processor->volumeHandle != INVALID_HANDLE_VALUE)
    {
        CloseHandle(processor->volumeHandle);
        processor->volumeHandle = INVALID_HANDLE_VALUE;
    }

    if (processor->frnMap)
    {
        free(processor->frnMap);
        processor->frnMap = NULL;
    }
}

// USN Processing Thread
DWORD WINAPI USNProcessingThread(LPVOID lpParam)
{
    WCHAR driveLetter = *(WCHAR *)lpParam;
    VolumeProcessor processor = {0};

    // 设置线程的字符编码支持
    SetConsoleOutputCP(CP_UTF8);
    SetConsoleCP(CP_UTF8);

    printf("=== Starting USN scan for drive %c: ===\n", (char)driveLetter);

    // Open volume
    printf("Drive %c: Opening volume...\n", (char)driveLetter);
    if (!OpenVolume(&processor, driveLetter))
    {
        printf("Drive %c: ERROR - Failed to open volume (check permissions)\n", (char)driveLetter);
        return 1;
    }
    printf("Drive %c: Volume opened successfully\n", (char)driveLetter);

    // Create USN Journal
    printf("Drive %c: Creating/accessing USN Journal...\n", (char)driveLetter);
    if (!CreateUSNJournal(&processor))
    {
        printf("Drive %c: ERROR - Failed to create USN journal\n", (char)driveLetter);
        CloseVolume(&processor);
        return 1;
    }
    printf("Drive %c: USN Journal ready\n", (char)driveLetter);

    // Query USN Journal
    printf("Drive %c: Querying USN Journal information...\n", (char)driveLetter);
    if (!QueryUSNJournal(&processor))
    {
        printf("Drive %c: ERROR - Failed to query USN journal\n", (char)driveLetter);
        DeleteUSNJournal(&processor);
        CloseVolume(&processor);
        return 1;
    }
    printf("Drive %c: USN Journal query successful\n", (char)driveLetter);

    // Enumerate USN Data
    printf("Drive %c: Starting file enumeration (this may take several minutes)...\n", (char)driveLetter);
    if (!EnumerateUSNData(&processor))
    {
        printf("Drive %c: ERROR - Failed to enumerate USN data\n", (char)driveLetter);
        DeleteUSNJournal(&processor);
        CloseVolume(&processor);
        return 1;
    }
    printf("Drive %c: File enumeration completed\n", (char)driveLetter);

    // Process files to database
    printf("Drive %c: Processing files to database...\n", (char)driveLetter);
    ProcessFilesToDatabase(&processor);
    printf("Drive %c: Database processing completed\n", (char)driveLetter);

    // Clean up
    DeleteUSNJournal(&processor);
    CloseVolume(&processor);

    printf("=== Drive %c: USN scan completed successfully ===\n", (char)driveLetter);
    return 0;
}

// Process all available volumes
BOOL ProcessAllVolumes()
{
    if (g_processing)
    {
        printf("USN processing already in progress\n");
        return FALSE;
    }

    g_processing = TRUE;
    printf("Starting full disk USN journal scan...\n");

    // Database functionality disabled - using USN memory only
    printf("Database functionality disabled - using USN memory only\n");

    // Get available drives and scan each NTFS volume
    printf("Detecting available disk drives...\n");
    DWORD drives = GetLogicalDrives();
    HANDLE threads[26];
    WCHAR driveLetters[26];
    DWORD threadCount = 0;

    // First pass: detect and report all NTFS drives
    printf("Available drives:\n");
    for (int i = 0; i < 26; i++)
    {
        if (drives & (1 << i))
        {
            WCHAR driveLetter = L'A' + i;
            WCHAR rootPath[4] = {driveLetter, L':', L'\\', L'\0'};

            // Check if it's NTFS
            WCHAR fileSystem[32];
            if (GetVolumeInformationW(rootPath, NULL, 0, NULL, NULL, NULL,
                                      fileSystem, sizeof(fileSystem) / sizeof(WCHAR)))
            {
                printf("  Drive %c: - File System: %ls", (char)driveLetter, fileSystem);
                if (wcscmp(fileSystem, L"NTFS") == 0)
                {
                    printf(" [Will scan with USN Journal]\n");
                    driveLetters[threadCount] = driveLetter;
                    threads[threadCount] = CreateThread(NULL, 0, USNProcessingThread,
                                                        &driveLetters[threadCount], 0, NULL);
                    if (threads[threadCount])
                    {
                        threadCount++;
                    }
                    else
                    {
                        printf("    ERROR: Failed to create thread for drive %c:\n", (char)driveLetter);
                    }
                }
                else
                {
                    printf(" [Skipped - Not NTFS]\n");
                }
            }
            else
            {
                printf("  Drive %c: - Unable to get file system info\n", (char)driveLetter);
            }
        }
    }

    printf("Created %d scanning threads for NTFS drives\n", threadCount);

    // Wait for all threads to complete
    if (threadCount > 0)
    {
        printf("Waiting for all %d disk scanning threads to complete...\n", threadCount);
        printf("This may take several minutes depending on disk size and file count.\n");

        DWORD waitResult = WaitForMultipleObjects(threadCount, threads, TRUE, INFINITE);

        if (waitResult == WAIT_OBJECT_0)
        {
            printf("All disk scanning threads completed successfully!\n");
        }
        else
        {
            printf("Warning: Some threads may not have completed normally (result: %d)\n", waitResult);
        }

        // Close thread handles
        for (DWORD i = 0; i < threadCount; i++)
        {
            CloseHandle(threads[i]);
        }

        // Report final statistics and verify scan success
        printf("Generating final scan report...\n");
        ReportScanStatistics();

        // Verify that files were actually scanned
        BOOL scanSuccessful = VerifyScanSuccess();

        g_processing = FALSE;

        if (scanSuccessful)
        {
            printf("=== Full Disk USN Journal Scan COMPLETED Successfully ===\n");
            printf("Scan verification: Files were successfully indexed\n");
            return TRUE;
        }
        else
        {
            printf("=== Full Disk USN Journal Scan FAILED ===\n");
            printf("Scan verification: No files were indexed - scan failed\n");
            return FALSE;
        }
    }
    else
    {
        printf("No NTFS drives found to scan!\n");
        g_processing = FALSE;
        return FALSE;
    }
}

// Verify scan success (disabled - using USN memory only)
BOOL VerifyScanSuccess()
{
    printf("Scan verification disabled - using USN memory only\n");
    return TRUE; // Always return success
}

// Report scan statistics (disabled - using USN memory only)
void ReportScanStatistics()
{
    printf("Scan statistics disabled - using USN memory only\n");
    return;
}

// 处理单个USN记录
BOOL ProcessSingleUSNRecord(VolumeProcessor *processor, PUSN_RECORD_V2 usnRecord, HWND mainWindow)
{
    if (!usnRecord || !processor)
        return FALSE;

    // 提取文件名
    WCHAR fileName[MAX_PATH];
    DWORD nameLength = usnRecord->FileNameLength / sizeof(WCHAR);
    if (nameLength >= MAX_PATH)
        nameLength = MAX_PATH - 1;

    wcsncpy(fileName, usnRecord->FileName, nameLength);
    fileName[nameLength] = L'\0';

    // 通过FRN重建完整路径
    WCHAR fullPathW[MAX_PATH_LENGTH];
    if (!GetFullPath(processor, usnRecord->FileReferenceNumber, fullPathW, MAX_PATH_LENGTH))
    {
        // 如果无法重建路径，跳过此记录
        return FALSE;
    }

    // 转换为UTF-8
    char fullPath[MAX_PATH_LENGTH];
    char fileNameUtf8[MAX_PATH];
    WCharToChar(fullPathW, fullPath, sizeof(fullPath));
    WCharToChar(fileName, fileNameUtf8, sizeof(fileNameUtf8));

    // 获取文件信息
    LONGLONG fileSize = 0;
    char modifiedTime[64] = "Unknown";

    WIN32_FILE_ATTRIBUTE_DATA fileData;
    if (GetFileAttributesExW(fullPathW, GetFileExInfoStandard, &fileData))
    {
        LARGE_INTEGER size;
        size.LowPart = fileData.nFileSizeLow;
        size.HighPart = fileData.nFileSizeHigh;
        fileSize = size.QuadPart;

        ConvertFileTimeToString(&fileData.ftLastWriteTime, modifiedTime, sizeof(modifiedTime));
    }

    printf("USN Change: %s -> %s (Reason: 0x%x, Size: %lld)\n",
           fileNameUtf8, fullPath, usnRecord->Reason, fileSize);

    // 根据变化原因更新数据库
    BOOL updated = FALSE;
    if (usnRecord->Reason & (USN_REASON_FILE_CREATE | USN_REASON_RENAME_NEW_NAME))
    {
        // 文件创建或重命名 - 添加到数据库
        if (InsertFileRecord(fileNameUtf8, fullPath, fileSize, modifiedTime) == 0)
        {
            updated = TRUE;
        }
    }
    else if (usnRecord->Reason & USN_REASON_FILE_DELETE)
    {
        // 文件删除 - 从数据库移除
        if (DeleteFileRecord(fullPath) == 0)
        {
            updated = TRUE;
        }
    }
    else if (usnRecord->Reason & (USN_REASON_DATA_OVERWRITE | USN_REASON_DATA_EXTEND | USN_REASON_DATA_TRUNCATION))
    {
        // 文件修改 - 更新数据库
        if (UpdateFileRecord(fullPath, fileSize, modifiedTime) == 0)
        {
            updated = TRUE;
        }
    }

    // 如果有更新，通知主窗口刷新ListView
    if (updated && mainWindow)
    {
        PostMessage(mainWindow, WM_USER + 2, 0, 0); // 自定义消息通知刷新
    }

    return updated;
}

// 处理实时USN Journal变化
BOOL ProcessUSNJournalChanges(VolumeProcessor *processor, HWND mainWindow)
{
    if (!processor || !processor->volumeHandle || processor->volumeHandle == INVALID_HANDLE_VALUE)
    {
        printf("Invalid volume processor or handle\n");
        return FALSE;
    }

    // 设置读取USN Journal数据的参数
    READ_USN_JOURNAL_DATA readData = {0};
    readData.StartUsn = processor->nextUsn;
    readData.ReasonMask = USN_REASON_FILE_CREATE | USN_REASON_FILE_DELETE |
                          USN_REASON_DATA_OVERWRITE | USN_REASON_DATA_EXTEND |
                          USN_REASON_RENAME_NEW_NAME | USN_REASON_RENAME_OLD_NAME;
    readData.ReturnOnlyOnClose = FALSE;
    readData.Timeout = 0;
    readData.BytesToWaitFor = 0;
    readData.UsnJournalID = processor->journalId;

    CHAR buffer[USN_BUFFER_SIZE];
    DWORD bytesReturned;

    // 读取USN Journal变化
    if (!DeviceIoControl(
            processor->volumeHandle,
            FSCTL_READ_USN_JOURNAL,
            &readData,
            sizeof(readData),
            buffer,
            USN_BUFFER_SIZE,
            &bytesReturned,
            NULL))
    {
        DWORD error = GetLastError();
        if (error != ERROR_JOURNAL_ENTRY_DELETED)
        {
            printf("Failed to read USN journal: %lu\n", error);
        }
        return FALSE;
    }

    if (bytesReturned <= sizeof(USN))
    {
        return TRUE; // 没有新的变化
    }

    // 处理USN记录
    DWORD dwRetBytes = bytesReturned - sizeof(USN);
    PUSN_RECORD_V2 usnRecord = (PUSN_RECORD_V2)((PCHAR)buffer + sizeof(USN));
    BOOL hasChanges = FALSE;

    while (dwRetBytes > 0)
    {
        // 处理单个USN记录
        if (ProcessSingleUSNRecord(processor, usnRecord, mainWindow))
        {
            hasChanges = TRUE;
        }

        // 更新下一个USN位置
        processor->nextUsn = usnRecord->Usn;

        // 移动到下一个记录
        DWORD recordLength = usnRecord->RecordLength;
        if (recordLength == 0 || recordLength > dwRetBytes)
            break;

        dwRetBytes -= recordLength;
        usnRecord = (PUSN_RECORD_V2)((PCHAR)usnRecord + recordLength);
    }

    return hasChanges;
}

// ConvertFileTimeToString function is defined in file_watcher.c

// 宽字符版递归扫描函数
int ScanDirectoryRecursiveW(const WCHAR *dirPath, int depth)
{
    WIN32_FIND_DATAW findData;
    HANDLE hFind;
    WCHAR searchPath[MAX_PATH];
    int fileCount = 0;

    // 限制递归深度，避免过深的目录结构
    const int MAX_DEPTH = 10;
    if (depth > MAX_DEPTH)
    {
        wprintf(L"  达到最大递归深度，跳过: %ls\n", dirPath);
        return 0;
    }

    // 跳过系统保护目录
    if (wcsstr(dirPath, L"System Volume Information") ||
        wcsstr(dirPath, L"$Recycle.Bin") ||
        wcsstr(dirPath, L"Recovery") ||
        wcsstr(dirPath, L"hiberfil.sys") ||
        wcsstr(dirPath, L"pagefile.sys"))
    {
        return 0;
    }

    // 创建搜索模式
    swprintf(searchPath, MAX_PATH, L"%ls\\*", dirPath);

    hFind = FindFirstFileW(searchPath, &findData);
    if (hFind == INVALID_HANDLE_VALUE)
    {
        DWORD error = GetLastError();
        if (error != ERROR_ACCESS_DENIED)
        {
            wprintf(L"  无法访问目录: %ls (错误: %d)\n", dirPath, error);
        }
        return 0;
    }

    do
    {
        // 跳过 . 和 ..
        if (wcscmp(findData.cFileName, L".") == 0 || wcscmp(findData.cFileName, L"..") == 0)
            continue;

        // 拼接完整路径
        WCHAR fullPath[MAX_PATH];
        swprintf(fullPath, MAX_PATH, L"%ls\\%ls", dirPath, findData.cFileName);

        LARGE_INTEGER fileSize;
        fileSize.LowPart = findData.nFileSizeLow;
        fileSize.HighPart = findData.nFileSizeHigh;

        char timeStr[64];
        ConvertFileTimeToString(&findData.ftLastWriteTime, timeStr, sizeof(timeStr));

        // 插入数据库前转为UTF-8
        char fileNameUtf8[MAX_PATH * 3];
        char fullPathUtf8[MAX_PATH * 6];
        WCharToChar(findData.cFileName, fileNameUtf8, sizeof(fileNameUtf8));
        WCharToChar(fullPath, fullPathUtf8, sizeof(fullPathUtf8));

        // **无论是文件还是目录都插入数据库**
        if (InsertFileRecord(fileNameUtf8, fullPathUtf8, fileSize.QuadPart, timeStr) == 0)
        {
            fileCount++;
            if (fileCount % 100 == 0)
            {
                wprintf(L"  已索引 %d 个文件/目录...\n", fileCount);
            }
        }

        // 如果是目录，递归
        if (findData.dwFileAttributes & FILE_ATTRIBUTE_DIRECTORY)
        {
            ScanDirectoryRecursiveW(fullPath, depth + 1);
        }

        // 递归时，所有目录和文件都要遍历
        wprintf(L"[递归] 发现文件/目录: %ls\n", findData.cFileName);

    } while (FindNextFileW(hFind, &findData));

    FindClose(hFind);

    if (depth == 0)
    {
        wprintf(L"  目录 %ls 扫描完成，找到 %d 个文件\n", dirPath, fileCount);
    }

    return fileCount;
}

// 修改SimpleFallbackScan，调用宽字符版递归扫描
int SimpleFallbackScan()
{
    printf("Fallback scan disabled - using USN memory only\n");
    return 0; // Return success
}

int GetUSNFileCount()
{

    int totalUSNFiles = 0;
    DWORD drives = GetLogicalDrives();

    for (int i = 0; i < 26; i++)
    {
        if (drives & (1 << i))
        {
            WCHAR driveLetter = L'A' + i;
            WCHAR rootPath[4] = {driveLetter, L':', L'\\', L'\0'};

            WCHAR fileSystem[32];
            if (GetVolumeInformationW(rootPath, NULL, 0, NULL, NULL, NULL,
                                      fileSystem, sizeof(fileSystem) / sizeof(WCHAR)))
            {
                if (wcscmp(fileSystem, L"NTFS") == 0)
                {

                    int driveFileCount = CountUSNFilesOnDrive(driveLetter);
                    if (driveFileCount > 0)
                    {
                        totalUSNFiles += driveFileCount;
                    }
                    else
                    {

                        ULARGE_INTEGER freeBytesAvailable, totalBytes, totalFreeBytes;
                        if (GetDiskFreeSpaceExW(rootPath, &freeBytesAvailable, &totalBytes, &totalFreeBytes))
                        {
                            LONGLONG totalGB = totalBytes.QuadPart / (1024 * 1024 * 1024);
                            int estimatedFiles = 0;

                            if (driveLetter == 'C')
                            {
                                estimatedFiles = (int)(totalGB * 100);
                                if (estimatedFiles < 5000)
                                    estimatedFiles = 5000;
                                if (estimatedFiles > 50000)
                                    estimatedFiles = 50000;
                            }
                            else
                            {
                                estimatedFiles = (int)(totalGB * 50);
                                if (estimatedFiles < 1000)
                                    estimatedFiles = 1000;
                                if (estimatedFiles > 20000)
                                    estimatedFiles = 20000;
                            }

                            totalUSNFiles += estimatedFiles;
                        }
                        else
                        {
                            int defaultFiles = (driveLetter == 'C') ? 10000 : 2000;
                            totalUSNFiles += defaultFiles;
                        }
                    }
                }
            }
        }
    }

    return totalUSNFiles;
}

int CountUSNFilesOnDrive(WCHAR driveLetter)
{
    WCHAR volumePath[8];
    swprintf(volumePath, 8, L"\\\\.\\%c:", driveLetter);

    HANDLE hVolume = CreateFileW(volumePath, GENERIC_READ,
                                 FILE_SHARE_READ | FILE_SHARE_WRITE, NULL,
                                 OPEN_EXISTING, 0, NULL);

    if (hVolume == INVALID_HANDLE_VALUE)
    {
        return -1;
    }

    USN_JOURNAL_DATA journalData;
    DWORD bytesReturned;

    if (!DeviceIoControl(hVolume, FSCTL_QUERY_USN_JOURNAL, NULL, 0,
                         &journalData, sizeof(journalData), &bytesReturned, NULL))
    {
        CloseHandle(hVolume);
        return -1;
    }

    MFT_ENUM_DATA mftEnumData = {0};
    mftEnumData.StartFileReferenceNumber = 0;
    mftEnumData.LowUsn = journalData.FirstUsn;
    mftEnumData.HighUsn = journalData.NextUsn;

    BYTE buffer[64 * 1024];
    int fileCount = 0;

    while (TRUE)
    {
        DWORD bytesReturned = 0;
        if (!DeviceIoControl(hVolume, FSCTL_ENUM_USN_DATA, &mftEnumData, sizeof(mftEnumData),
                             buffer, sizeof(buffer), &bytesReturned, NULL))
        {
            DWORD error = GetLastError();
            if (error == ERROR_HANDLE_EOF)
            {
                break;
            }
            else
            {
                break;
            }
        }

        // Parse USN records
        DWORD offset = sizeof(USN);
        while (offset < bytesReturned)
        {
            PUSN_RECORD_V2 usnRecord = (PUSN_RECORD_V2)(buffer + offset);

            if (usnRecord->RecordLength == 0)
                break;

            // Count only files (not directories)
            if (!(usnRecord->FileAttributes & FILE_ATTRIBUTE_DIRECTORY))
            {
                fileCount++;
            }

            offset += usnRecord->RecordLength;
        }

        // Update for next iteration
        if (bytesReturned > sizeof(USN))
        {
            mftEnumData.StartFileReferenceNumber = *(USN *)buffer;
        }
        else
        {
            break;
        }
    }

    CloseHandle(hVolume);

    return fileCount;
}

// 宽字符版简单目录扫描函数
int ScanDirectorySimpleW(const WCHAR *dirPath)
{
    WIN32_FIND_DATAW findData;
    HANDLE hFind;
    WCHAR searchPath[MAX_PATH];
    int fileCount = 0;

    // 创建搜索模式
    swprintf(searchPath, MAX_PATH, L"%ls\\*", dirPath);

    hFind = FindFirstFileW(searchPath, &findData);
    if (hFind == INVALID_HANDLE_VALUE)
    {

        return 0;
    }

    do
    {

        if (wcscmp(findData.cFileName, L".") == 0 || wcscmp(findData.cFileName, L"..") == 0)
            continue;

        WCHAR fullPath[MAX_PATH];
        swprintf(fullPath, MAX_PATH, L"%ls\\%ls", dirPath, findData.cFileName);

        LARGE_INTEGER fileSize;
        fileSize.LowPart = findData.nFileSizeLow;
        fileSize.HighPart = findData.nFileSizeHigh;

        char timeStr[64];
        ConvertFileTimeToString(&findData.ftLastWriteTime, timeStr, sizeof(timeStr));

        char fileNameUtf8[MAX_PATH * 3];
        char fullPathUtf8[MAX_PATH * 6];
        WCharToChar(findData.cFileName, fileNameUtf8, sizeof(fileNameUtf8));
        WCharToChar(fullPath, fullPathUtf8, sizeof(fullPathUtf8));

        if (InsertFileRecord(fileNameUtf8, fullPathUtf8, fileSize.QuadPart, timeStr) == 0)
        {
            fileCount++;
        }

        if (findData.dwFileAttributes & FILE_ATTRIBUTE_DIRECTORY)
        {
            ScanDirectorySimpleW(fullPath);
        }

        if (fileCount >= 2000)
        {

            break;
        }

    } while (FindNextFileW(hFind, &findData));

    FindClose(hFind);

    return fileCount;
}

// Recursive directory scanning function for full disk scan
int ScanDirectoryRecursive(const char *dirPath, int depth)
{
    WIN32_FIND_DATAA findData;
    HANDLE hFind;
    char searchPath[MAX_PATH];
    int fileCount = 0;

    // 限制递归深度，避免过深的目录结构
    const int MAX_DEPTH = 10;
    if (depth > MAX_DEPTH)
    {
        printf("  达到最大递归深度，跳过: %s\n", dirPath);
        return 0;
    }

    // 跳过系统保护目录
    if (strstr(dirPath, "System Volume Information") ||
        strstr(dirPath, "$Recycle.Bin") ||
        strstr(dirPath, "Recovery") ||
        strstr(dirPath, "hiberfil.sys") ||
        strstr(dirPath, "pagefile.sys"))
    {
        return 0;
    }

    // Create search pattern
    snprintf(searchPath, sizeof(searchPath), "%s\\*", dirPath);

    hFind = FindFirstFileA(searchPath, &findData);
    if (hFind == INVALID_HANDLE_VALUE)
    {
        DWORD error = GetLastError();
        if (error != ERROR_ACCESS_DENIED) // 忽略访问被拒绝的错误
        {
            printf("  无法访问目录: %s (错误: %d)\n", dirPath, error);
        }
        return 0;
    }

    do
    {
        // Skip . and .. entries
        if (strcmp(findData.cFileName, ".") == 0 || strcmp(findData.cFileName, "..") == 0)
            continue;

        // Create full path
        char fullPath[MAX_PATH];
        snprintf(fullPath, sizeof(fullPath), "%s\\%s", dirPath, findData.cFileName);

        if (findData.dwFileAttributes & FILE_ATTRIBUTE_DIRECTORY)
        {
            // 递归扫描子目录
            fileCount += ScanDirectoryRecursive(fullPath, depth + 1);
        }
        else
        {
            // 处理文件
            LARGE_INTEGER fileSize;
            fileSize.LowPart = findData.nFileSizeLow;
            fileSize.HighPart = findData.nFileSizeHigh;

            // Convert file time to string (use actual file modification time)
            char timeStr[64];
            ConvertFileTimeToString(&findData.ftLastWriteTime, timeStr, sizeof(timeStr));

            // Insert into database
            if (InsertFileRecord(findData.cFileName, fullPath, fileSize.QuadPart, timeStr) == 0)
            {
                fileCount++;
            }
        }

    } while (FindNextFileA(hFind, &findData));

    FindClose(hFind);

    return fileCount;
}
