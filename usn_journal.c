#include "usn_journal.h"

// ============================================================================
// 全局变量和常量定义
// ============================================================================
static BOOL g_processing = FALSE;

// ============================================================================
// 核心USN Journal扫描功能
// ============================================================================

// ============================================================================
// 多线程USN扫描实现
// ============================================================================

// 兼容性函数（占位符）
int ReadUSNToMemory() { return ReadUSNToMemoryMultiThreaded(); }
int ScanAllDrives() { return ReadUSNToMemoryMultiThreaded(); }

// ============================================================================
// 简化的目录扫描函数（回退方案）
// ============================================================================

// 简化的目录扫描（当USN Journal不可用时的回退方案）
int ScanDirectoryWithDepth(const WCHAR *dirPath, int *totalCount, int maxFiles, int currentDepth, int maxDepth)
{
    WIN32_FIND_DATAW findData;
    HANDLE hFind;
    WCHAR searchPath[MAX_PATH * 2];

    if (currentDepth > maxDepth || *totalCount >= maxFiles)
        return 0;

    swprintf(searchPath, MAX_PATH * 2,
             (wcslen(dirPath) > 0 && dirPath[wcslen(dirPath) - 1] == L'\\') ? L"%s*" : L"%s\\*",
             dirPath);

    hFind = FindFirstFileW(searchPath, &findData);
    if (hFind == INVALID_HANDLE_VALUE)
        return 0;

    do
    {
        if (wcscmp(findData.cFileName, L".") == 0 || wcscmp(findData.cFileName, L"..") == 0)
            continue;

        if (*totalCount >= maxFiles)
            break;

        WCHAR fullPath[MAX_PATH * 2];
        swprintf(fullPath, MAX_PATH * 2,
                 (wcslen(dirPath) > 0 && dirPath[wcslen(dirPath) - 1] == L'\\') ? L"%s%s" : L"%s\\%s",
                 dirPath, findData.cFileName);

        LONGLONG fileSize = ((LONGLONG)findData.nFileSizeHigh << 32) + findData.nFileSizeLow;
        AddUSNFile(findData.cFileName, fullPath, fileSize, findData.ftLastWriteTime, findData.dwFileAttributes);
        (*totalCount)++;

        if ((findData.dwFileAttributes & FILE_ATTRIBUTE_DIRECTORY) &&
            wcslen(fullPath) < (MAX_PATH - 50) && currentDepth < maxDepth)
        {
            ScanDirectoryWithDepth(fullPath, totalCount, maxFiles, currentDepth + 1, maxDepth);
        }

    } while (FindNextFileW(hFind, &findData) && *totalCount < maxFiles);

    FindClose(hFind);
    return 0;
}

int ReadUSNToMemoryMultiThreaded()
{

    DWORD drives = GetLogicalDrives();
    DriveThreadData driveData[MAX_DRIVES];
    HANDLE threads[MAX_DRIVES];
    int driveCount = 0;
    int totalCount = 0;
    DWORD startTime = GetTickCount();

    for (int i = 0; i < 26; i++)
    {
        if (drives & (1 << i))
        {
            WCHAR driveLetter = L'A' + i;
            WCHAR drivePath[4] = {driveLetter, L':', L'\\', L'\0'};

            // 检查驱动器类型
            UINT driveType = GetDriveTypeW(drivePath);
            if (driveType == DRIVE_FIXED && driveCount < MAX_DRIVES)
            {
                wcscpy(driveData[driveCount].drivePath, drivePath);
                driveData[driveCount].threadId = driveCount;
                driveData[driveCount].fileCount = 0;
                driveData[driveCount].scanTime = 0;
                driveData[driveCount].success = FALSE;
                driveCount++;
            }
        }
    }

    for (int i = 0; i < driveCount; i++)
    {
        threads[i] = CreateThread(NULL, 0, DriveThreadProc, &driveData[i], 0, NULL);
        if (threads[i] == NULL)
        {
        }
        else
        {
        }
    }

    // 等待所有线程完成
    WaitForMultipleObjects(driveCount, threads, TRUE, INFINITE);

    // 收集结果
    for (int i = 0; i < driveCount; i++)
    {
        if (driveData[i].success)
            totalCount += driveData[i].fileCount;

        if (threads[i])
            CloseHandle(threads[i]);
    }

    return 0;
}

// 驱动器扫描线程函数
DWORD WINAPI DriveThreadProc(LPVOID lpParam)
{
    DriveThreadData *data = (DriveThreadData *)lpParam;
    DWORD startTime = GetTickCount();

    int fileCount = ScanDriveUSN(data->drivePath);

    data->scanTime = GetTickCount() - startTime;
    data->fileCount = fileCount;
    data->success = (fileCount > 0);

    if (!data->success)
    {
        // 回退到递归文件扫描
        int fallbackCount = FallbackDirectoryScan(data->drivePath);
        if (fallbackCount > 0)
        {
            data->fileCount = fallbackCount;
            data->success = TRUE;
            data->scanTime = GetTickCount() - startTime;
        }
    }

    return 0;
}

// 扫描单个驱动器的USN Journal
int ScanDriveUSN(const WCHAR *drivePath)
{
    HANDLE hVolume;
    WCHAR volumePath[MAX_PATH];

    // 构建卷路径 \\.\C:
    swprintf(volumePath, MAX_PATH, L"\\\\.\\%c:", drivePath[0]);

    // 打开卷
    hVolume = CreateFileW(volumePath,
                          GENERIC_READ,
                          FILE_SHARE_READ | FILE_SHARE_WRITE,
                          NULL,
                          OPEN_EXISTING,
                          0,
                          NULL);

    if (hVolume == INVALID_HANDLE_VALUE)
        return 0;

    // 查询USN Journal信息
    USN_JOURNAL_DATA journalData;
    DWORD bytesReturned;

    if (!DeviceIoControl(hVolume,
                         FSCTL_QUERY_USN_JOURNAL,
                         NULL, 0,
                         &journalData, sizeof(journalData),
                         &bytesReturned,
                         NULL))
    {
        CloseHandle(hVolume);
        return 0;
    }

    // 枚举USN记录
    int fileCount = EnumerateUSNRecords(hVolume, drivePath);

    CloseHandle(hVolume);
    return fileCount;
}

// 优化的USN记录枚举函数
int EnumerateUSNRecords(HANDLE hVolume, const WCHAR *drivePath)
{
    // 使用动态分配的大缓冲区
    BYTE *buffer = (BYTE *)malloc(USN_BUFFER_SIZE);
    if (!buffer)
        return 0;

    MFT_ENUM_DATA mftEnumData = {0};
    DWORD bytesReturned;
    int fileCount = 0;
    int batchCount = 0;
    DWORD startTime = GetTickCount();

    // 设置枚举参数
    mftEnumData.StartFileReferenceNumber = 0;
    mftEnumData.LowUsn = 0;
    mftEnumData.HighUsn = MAXLONGLONG;

    while (TRUE)
    {
        // 枚举USN记录
        if (!DeviceIoControl(hVolume,
                             FSCTL_ENUM_USN_DATA,
                             &mftEnumData, sizeof(mftEnumData),
                             buffer, USN_BUFFER_SIZE,
                             &bytesReturned,
                             NULL))
        {
            DWORD error = GetLastError();
            if (error == ERROR_HANDLE_EOF)
                break;
            else
                break;
        }

        if (bytesReturned < sizeof(USN))
            break;

        // 批量处理USN记录
        int batchProcessed = ProcessUSNBatch(buffer, bytesReturned, drivePath, &mftEnumData);
        fileCount += batchProcessed;
        batchCount++;

        // 限制文件数量避免内存溢出
        if (fileCount >= 4069000)
            break;
    }

    free(buffer);
    return fileCount;
}

// 批量处理USN记录
int ProcessUSNBatch(BYTE *buffer, DWORD bytesReturned, const WCHAR *drivePath, MFT_ENUM_DATA *mftEnumData)
{
    int processedCount = 0;
    PUSN_RECORD_V2 usnRecord;
    DWORD offset = sizeof(USN);

    while (offset < bytesReturned)
    {
        usnRecord = (PUSN_RECORD_V2)(buffer + offset);

        if (usnRecord->RecordLength == 0 || usnRecord->RecordLength > (bytesReturned - offset))
            break;

        // 快速处理USN记录
        ProcessUSNRecordFast(usnRecord, drivePath);
        processedCount++;

        // 更新下一个记录的起始位置
        mftEnumData->StartFileReferenceNumber = usnRecord->FileReferenceNumber;

        // 移动到下一个记录
        offset += usnRecord->RecordLength;
    }

    return processedCount;
}

// 优化的USN记录处理函数
void ProcessUSNRecordFast(PUSN_RECORD_V2 usnRecord, const WCHAR *drivePath)
{
    // 跳过目录和系统文件，只处理普通文件
    if (usnRecord->FileAttributes & FILE_ATTRIBUTE_DIRECTORY)
        return;

    if (usnRecord->FileAttributes & (FILE_ATTRIBUTE_SYSTEM | FILE_ATTRIBUTE_HIDDEN))
        return;

    // 快速提取文件名（避免不必要的复制）
    WCHAR *fileName = (WCHAR *)((BYTE *)usnRecord + usnRecord->FileNameOffset);
    int fileNameLength = usnRecord->FileNameLength / sizeof(WCHAR);

    // 跳过临时文件和系统文件
    if (fileNameLength > 4)
    {
        WCHAR *ext = fileName + fileNameLength - 4;
        if (wcsncmp(ext, L".tmp", 4) == 0 || wcsncmp(ext, L".log", 4) == 0)
            return;
    }

    // 构建完整路径（优化：减少字符串操作）
    WCHAR fullPath[MAX_PATH];
    swprintf(fullPath, MAX_PATH, L"%ls%.*ls", drivePath, fileNameLength, fileName);

    // 获取文件大小（USN记录中没有直接的文件大小信息，设为0）
    LONGLONG fileSize = 0;

    // 转换时间戳
    FILETIME lastWriteTime;
    lastWriteTime.dwLowDateTime = usnRecord->TimeStamp.LowPart;
    lastWriteTime.dwHighDateTime = usnRecord->TimeStamp.HighPart;

    // 添加到内存数组
    AddUSNFile(fileName, fullPath, fileSize, lastWriteTime, usnRecord->FileAttributes);
}

// ============================================================================
// USN记录处理函数（已移除未使用的ProcessUSNRecord）
// ============================================================================

// 回退的普通目录扫描 - 优化版本，避免卡死
int FallbackDirectoryScan(const WCHAR *drivePath)
{
    int totalCount = 0;
    int maxFiles = 4069000;

    // 使用带深度控制的递归扫描函数
    ScanDirectoryWithDepth(drivePath, &totalCount, maxFiles, 0, 16);

    return totalCount;
}

// 简单的非递归扫描 - 作为最后的备用方案
int SimpleDirectoryScan(const WCHAR *drivePath)
{
    int totalCount = 0;
    WIN32_FIND_DATAW findData;
    HANDLE hFind;
    WCHAR searchPath[MAX_PATH];

    swprintf(searchPath, MAX_PATH, L"%s*", drivePath);
    hFind = FindFirstFileW(searchPath, &findData);

    if (hFind != INVALID_HANDLE_VALUE)
    {
        do
        {
            if (wcscmp(findData.cFileName, L".") != 0 && wcscmp(findData.cFileName, L"..") != 0)
            {
                WCHAR fullPath[MAX_PATH * 2];
                swprintf(fullPath, MAX_PATH * 2, L"%s%s", drivePath, findData.cFileName);

                LONGLONG fileSize = ((LONGLONG)findData.nFileSizeHigh << 32) + findData.nFileSizeLow;
                AddUSNFile(findData.cFileName, fullPath, fileSize, findData.ftLastWriteTime, findData.dwFileAttributes);
                totalCount++;

                if (totalCount >= 4069000)
                    break;
            }
        } while (FindNextFileW(hFind, &findData));

        FindClose(hFind);
    }

    return totalCount;
}

// ============================================================================
// 数据库操作函数（占位符）
// ============================================================================

int InitializeDatabase(const char *dbPath) { return 0; }
int InsertFileRecord(const char *name, const char *path, LONGLONG size, const char *modified) { return 0; }
int UpdateFileRecord(const char *path, LONGLONG size, const char *modified) { return 0; }
int DeleteFileRecord(const char *path) { return 0; }
int ClearDatabase() { return 0; }
void CloseDatabase() {}
int CompressDatabase() { return 0; }

// ============================================================================
// 未使用的复杂USN处理函数（占位符）
// ============================================================================

BOOL OpenVolume(VolumeProcessor *processor, WCHAR driveLetter) { return FALSE; }

BOOL CreateUSNJournal(VolumeProcessor *processor) { return FALSE; }
BOOL QueryUSNJournal(VolumeProcessor *processor) { return FALSE; }

void AddFrnEntry(VolumeProcessor *processor, DWORDLONG frn, DWORDLONG parentFrn, const WCHAR *fileName) {}
FrnEntry *FindFrnEntry(VolumeProcessor *processor, DWORDLONG frn) { return NULL; }

BOOL GetFullPath(VolumeProcessor *processor, DWORDLONG frn, WCHAR *fullPath, DWORD pathSize) { return FALSE; }

void FileTimeToString(const LARGE_INTEGER *fileTime, char *timeString, size_t bufferSize) {}
LONGLONG FileSizeToLongLong(const LARGE_INTEGER *fileSize) { return 0; }

void WCharToChar(const WCHAR *wstr, char *str, size_t bufferSize) {}

BOOL EnumerateUSNData(VolumeProcessor *processor) { return FALSE; }

BOOL ProcessFilesToDatabase(VolumeProcessor *processor) { return FALSE; }

// Delete USN Journal
BOOL DeleteUSNJournal(VolumeProcessor *processor)
{
    processor->deleteData.UsnJournalID = processor->journalData.UsnJournalID;
    processor->deleteData.DeleteFlags = USN_DELETE_FLAG_DELETE;

    DWORD bytesReturned;
    BOOL result = DeviceIoControl(
        processor->volumeHandle,
        FSCTL_DELETE_USN_JOURNAL,
        &processor->deleteData,
        sizeof(processor->deleteData),
        NULL,
        0,
        &bytesReturned,
        NULL);

    if (!result)
    {
        printf("Failed to delete USN journal: Error %lu\n", GetLastError());
    }

    return result;
}

// Close volume
void CloseVolume(VolumeProcessor *processor)
{
    if (processor->volumeHandle != INVALID_HANDLE_VALUE)
    {
        CloseHandle(processor->volumeHandle);
        processor->volumeHandle = INVALID_HANDLE_VALUE;
    }

    if (processor->frnMap)
    {
        free(processor->frnMap);
        processor->frnMap = NULL;
    }
}

// USN Processing Thread
DWORD WINAPI USNProcessingThread(LPVOID lpParam)
{
    WCHAR driveLetter = *(WCHAR *)lpParam;
    VolumeProcessor processor = {0};

    // 设置线程的字符编码支持
    SetConsoleOutputCP(CP_UTF8);
    SetConsoleCP(CP_UTF8);

    printf("=== Starting USN scan for drive %c: ===\n", (char)driveLetter);

    // Open volume
    printf("Drive %c: Opening volume...\n", (char)driveLetter);
    if (!OpenVolume(&processor, driveLetter))
    {
        printf("Drive %c: ERROR - Failed to open volume (check permissions)\n", (char)driveLetter);
        return 1;
    }
    printf("Drive %c: Volume opened successfully\n", (char)driveLetter);

    // Create USN Journal
    printf("Drive %c: Creating/accessing USN Journal...\n", (char)driveLetter);
    if (!CreateUSNJournal(&processor))
    {
        printf("Drive %c: ERROR - Failed to create USN journal\n", (char)driveLetter);
        CloseVolume(&processor);
        return 1;
    }
    printf("Drive %c: USN Journal ready\n", (char)driveLetter);

    // Query USN Journal
    printf("Drive %c: Querying USN Journal information...\n", (char)driveLetter);
    if (!QueryUSNJournal(&processor))
    {
        printf("Drive %c: ERROR - Failed to query USN journal\n", (char)driveLetter);
        DeleteUSNJournal(&processor);
        CloseVolume(&processor);
        return 1;
    }
    printf("Drive %c: USN Journal query successful\n", (char)driveLetter);

    // Enumerate USN Data
    printf("Drive %c: Starting file enumeration (this may take several minutes)...\n", (char)driveLetter);
    if (!EnumerateUSNData(&processor))
    {
        printf("Drive %c: ERROR - Failed to enumerate USN data\n", (char)driveLetter);
        DeleteUSNJournal(&processor);
        CloseVolume(&processor);
        return 1;
    }
    printf("Drive %c: File enumeration completed\n", (char)driveLetter);

    // Process files to database
    printf("Drive %c: Processing files to database...\n", (char)driveLetter);
    ProcessFilesToDatabase(&processor);
    printf("Drive %c: Database processing completed\n", (char)driveLetter);

    // Clean up
    DeleteUSNJournal(&processor);
    CloseVolume(&processor);

    printf("=== Drive %c: USN scan completed successfully ===\n", (char)driveLetter);
    return 0;
}

// Process all available volumes
BOOL ProcessAllVolumes()
{
    if (g_processing)
    {
        printf("USN processing already in progress\n");
        return FALSE;
    }

    g_processing = TRUE;
    printf("Starting full disk USN journal scan...\n");

    // Database functionality disabled - using USN memory only
    printf("Database functionality disabled - using USN memory only\n");

    // Get available drives and scan each NTFS volume
    printf("Detecting available disk drives...\n");
    DWORD drives = GetLogicalDrives();
    HANDLE threads[26];
    WCHAR driveLetters[26];
    DWORD threadCount = 0;

    // First pass: detect and report all NTFS drives
    printf("Available drives:\n");
    for (int i = 0; i < 26; i++)
    {
        if (drives & (1 << i))
        {
            WCHAR driveLetter = L'A' + i;
            WCHAR rootPath[4] = {driveLetter, L':', L'\\', L'\0'};

            // Check if it's NTFS
            WCHAR fileSystem[32];
            if (GetVolumeInformationW(rootPath, NULL, 0, NULL, NULL, NULL,
                                      fileSystem, sizeof(fileSystem) / sizeof(WCHAR)))
            {
                printf("  Drive %c: - File System: %ls", (char)driveLetter, fileSystem);
                if (wcscmp(fileSystem, L"NTFS") == 0)
                {
                    printf(" [Will scan with USN Journal]\n");
                    driveLetters[threadCount] = driveLetter;
                    threads[threadCount] = CreateThread(NULL, 0, USNProcessingThread,
                                                        &driveLetters[threadCount], 0, NULL);
                    if (threads[threadCount])
                    {
                        threadCount++;
                    }
                    else
                    {
                        printf("    ERROR: Failed to create thread for drive %c:\n", (char)driveLetter);
                    }
                }
                else
                {
                    printf(" [Skipped - Not NTFS]\n");
                }
            }
            else
            {
                printf("  Drive %c: - Unable to get file system info\n", (char)driveLetter);
            }
        }
    }

    printf("Created %d scanning threads for NTFS drives\n", threadCount);

    // Wait for all threads to complete
    if (threadCount > 0)
    {
        printf("Waiting for all %d disk scanning threads to complete...\n", threadCount);
        printf("This may take several minutes depending on disk size and file count.\n");

        DWORD waitResult = WaitForMultipleObjects(threadCount, threads, TRUE, INFINITE);

        if (waitResult == WAIT_OBJECT_0)
        {
            printf("All disk scanning threads completed successfully!\n");
        }
        else
        {
            printf("Warning: Some threads may not have completed normally (result: %d)\n", waitResult);
        }

        // Close thread handles
        for (DWORD i = 0; i < threadCount; i++)
        {
            CloseHandle(threads[i]);
        }

        // Report final statistics and verify scan success
        printf("Generating final scan report...\n");
        ReportScanStatistics();

        // Verify that files were actually scanned
        BOOL scanSuccessful = VerifyScanSuccess();

        g_processing = FALSE;

        if (scanSuccessful)
        {
            printf("=== Full Disk USN Journal Scan COMPLETED Successfully ===\n");
            printf("Scan verification: Files were successfully indexed\n");
            return TRUE;
        }
        else
        {
            printf("=== Full Disk USN Journal Scan FAILED ===\n");
            printf("Scan verification: No files were indexed - scan failed\n");
            return FALSE;
        }
    }
    else
    {
        printf("No NTFS drives found to scan!\n");
        g_processing = FALSE;
        return FALSE;
    }
}

// ============================================================================
// 统计和验证函数（占位符）
// ============================================================================

BOOL VerifyScanSuccess() { return TRUE; }
void ReportScanStatistics() {}

BOOL ProcessSingleUSNRecord(VolumeProcessor *processor, PUSN_RECORD_V2 usnRecord, HWND mainWindow) { return FALSE; }

BOOL ProcessUSNJournalChanges(VolumeProcessor *processor, HWND mainWindow) { return FALSE; }

int SimpleFallbackScan() { return 0; }

int GetUSNFileCount() { return 0; }

int CountUSNFilesOnDrive(WCHAR driveLetter) { return 0; }

int ScanDirectorySimpleW(const WCHAR *dirPath) { return 0; }

int ScanDirectoryRecursive(const char *dirPath, int depth) { return 0; }
