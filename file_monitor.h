#ifndef FILE_MONITOR_H
#define FILE_MONITOR_H

#include <windows.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

// File monitoring constants
#define MAX_DRIVES 26
#define BUFFER_SIZE 4096
#define MAX_FILENAME_LENGTH 260

// File action types (from Windows API)
#define FILE_ACTION_ADDED 0x00000001
#define FILE_ACTION_REMOVED 0x00000002
#define FILE_ACTION_MODIFIED 0x00000003
#define FILE_ACTION_RENAMED_OLD_NAME 0x00000004
#define FILE_ACTION_RENAMED_NEW_NAME 0x00000005

// Monitor flags
#define MONITOR_FLAGS (FILE_NOTIFY_CHANGE_FILE_NAME | \
                       FILE_NOTIFY_CHANGE_DIR_NAME |  \
                       FILE_NOTIFY_CHANGE_SIZE |      \
                       FILE_NOTIFY_CHANGE_LAST_WRITE)

// Drive monitor structure
typedef struct
{
    WCHAR driveLetter;
    HANDLE hDirectory;
    HANDLE hThread;
    OVERLAPPED overlapped;
    BYTE buffer[BUFFER_SIZE];
    BOOL isActive;
    BOOL shouldStop;
} DriveMonitor;

// File change event structure
typedef struct
{
    DWORD action;
    WCHAR fileName[MAX_FILENAME_LENGTH];
    WCHAR fullPath[MAX_PATH];
    LARGE_INTEGER fileSize;
    FILETIME lastWriteTime;
} FileChangeEvent;

// Global monitor manager
typedef struct
{
    DriveMonitor drives[MAX_DRIVES];
    int activeDriveCount;
    BOOL isInitialized;
    BOOL isMonitoring;
    HWND notifyWindow;
} FileMonitorManager;

// Function declarations
BOOL InitializeFileMonitor(HWND hwnd);
BOOL StartMonitoring();
BOOL StopMonitoring();
void CleanupFileMonitor();

// Drive monitoring functions
BOOL StartDriveMonitoring(DriveMonitor *monitor);
BOOL StopDriveMonitoring(DriveMonitor *monitor);
DWORD WINAPI DriveMonitorThread(LPVOID lpParam);

// Event processing functions
void ProcessFileChangeEvent(const FileChangeEvent *event);
void ProcessDirectoryChanges(DriveMonitor *monitor, BYTE *buffer, DWORD bufferSize);
void UpdateDatabaseForFileChange(const FileChangeEvent *event);

// Utility functions
BOOL GetFileInformation(const WCHAR *filePath, LARGE_INTEGER *fileSize, FILETIME *lastWriteTime);
void ConvertFileTimeToString(const FILETIME *fileTime, char *timeString, size_t bufferSize);
BOOL IsValidDriveForMonitoring(WCHAR driveLetter);

// Database update functions
BOOL AddFileToDatabase(const WCHAR *fileName, const WCHAR *fullPath,
                       const LARGE_INTEGER *fileSize, const FILETIME *lastWriteTime);
BOOL RemoveFileFromDatabase(const WCHAR *fullPath);
BOOL UpdateFileInDatabase(const WCHAR *fullPath, const LARGE_INTEGER *fileSize,
                          const FILETIME *lastWriteTime);
BOOL RenameFileInDatabase(const WCHAR *oldPath, const WCHAR *newPath);

// Configuration functions
BOOL ShouldPerformFullScan();
void SetFullScanCompleted();
BOOL IsFirstRun();

// Custom Windows messages for file change notifications
#define WM_FILE_ADDED (WM_USER + 200)
#define WM_FILE_REMOVED (WM_USER + 201)
#define WM_FILE_MODIFIED (WM_USER + 202)
#define WM_FILE_RENAMED (WM_USER + 203)

#endif // FILE_MONITOR_H
