# EveryView 图标打包实现说明

## 概述
成功将图标资源打包到 EveryView.exe 程序中，实现了自定义程序图标显示。

## 实现步骤

### 1. 资源文件配置
- **文件**: `resource.rc`
- **图标文件**: `resource/icons8_32.ico` (32x32像素)
- **资源ID**: `IDI_MAIN_ICON = 101`

### 2. 程序代码集成
在 `main.c` 中添加了图标加载代码：

```c
// 资源定义
#define IDI_MAIN_ICON 101

// 窗口类注册时设置图标
wc.hIcon = LoadIcon(hInstance, MAKEINTRESOURCE(IDI_MAIN_ICON));

// 窗口创建后设置图标
HICON hWindowIcon = LoadIcon(hInstance, MAKEINTRESOURCE(IDI_MAIN_ICON));
SendMessage(hwndMain, WM_SETICON, ICON_BIG, (LPARAM)hWindowIcon);
SendMessage(hwndMain, WM_SETICON, ICON_SMALL, (LPARAM)hWindowIcon);
```

### 3. 编译脚本优化
更新了 `build.bat` 文件，支持自动图标编译：

1. **第一步**: 编译资源文件 `windres resource.rc -o resource.o`
2. **第二步**: 链接资源到主程序
3. **备用方案**: 如果资源编译失败，自动回退到无图标版本

### 4. 编译命令
```batch
# 带图标编译
windres resource.rc -o resource.o
gcc -O2 -mwindows -o EveryView.exe main.c file_watcher.c usn_journal.c resource.o -lcomctl32 -luser32 -lgdi32 -lkernel32 -ladvapi32 -lshell32

# 不带图标编译（备用）
gcc -O2 -mwindows -o EveryView.exe main.c file_watcher.c usn_journal.c -lcomctl32 -luser32 -lgdi32 -lkernel32 -ladvapi32 -lshell32
```

## 文件结构
```
├── resource/
│   ├── icons8_16.ico    # 16x16 图标
│   ├── icons8_24.ico    # 24x24 图标
│   ├── icons8_32.ico    # 32x32 图标（主图标）
│   ├── icons8_48.ico    # 48x48 图标
│   ├── icons8_64.ico    # 64x64 图标
│   └── icons8_96.ico    # 96x96 图标
├── resource.rc          # 资源配置文件
├── main.c              # 主程序（包含图标加载代码）
├── build.bat           # 主编译脚本（带图标）
└── build_icon.bat      # 专用图标编译脚本
```

## 版本信息
资源文件还包含了程序版本信息：
- 公司名称: Everyview64
- 文件描述: Everyview64 - 文件预览器
- 版本: 1.0.0.0
- 版权: Copyright (C) 2024

## 使用方法
1. 运行 `build.bat` 进行编译
2. 如果成功，将生成带图标的 `EveryView.exe`
3. 如果资源编译失败，会自动生成无图标版本

## 优势
- 程序具有专业的自定义图标
- 在任务栏、文件管理器中显示自定义图标
- 编译过程自动化，有备用方案
- 支持多种尺寸图标，适应不同显示需求

## 注意事项
- 确保 `windres` 工具可用（MinGW 环境）
- 图标文件路径使用正斜杠 `/` 而非反斜杠 `\`
- 资源编译失败时会自动回退到无图标版本
