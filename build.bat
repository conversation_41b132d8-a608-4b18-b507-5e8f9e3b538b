@echo off
echo Building EveryView with icon...

echo Step 1: Compiling resource file...
windres resource.rc -o resource.o

if %errorlevel% neq 0 (
    echo Resource compilation failed! Building without icon...
    goto :compile_without_icon
)

echo Step 2: Compiling main program with resource...
gcc -O2 -mwindows -o EveryView.exe main.c file_watcher.c usn_journal.c resource.o -lcomctl32 -luser32 -lgdi32 -lkernel32 -ladvapi32 -lshell32 -lshcore

if %errorlevel% == 0 (
    echo Build successful! Generated EveryView.exe with icon
    del resource.o 2>nul
    goto :end
) else (
    echo Build with icon failed! Trying without icon...
    goto :compile_without_icon
)

:compile_without_icon
echo Building without icon...
gcc -O2 -mwindows -o EveryView.exe main.c file_watcher.c usn_journal.c -lcomctl32 -luser32 -lgdi32 -lkernel32 -ladvapi32 -lshell32 -lshcore
if %errorlevel% == 0 (
    echo Build successful! Generated EveryView.exe without icon
) else (
    echo Build failed completely!
)

:end
echo Cleaning up...
if exist resource.o del resource.o
echo Done!
pause