#include "file_monitor.h"
#include "sqlite3.h"

// Global monitor manager
static FileMonitorManager g_monitorManager = {0};
extern sqlite3 *g_database;

// Initialize file monitoring system
BOOL InitializeFileMonitor(HWND hwnd)
{
    if (g_monitorManager.isInitialized)
        return TRUE;

    memset(&g_monitorManager, 0, sizeof(FileMonitorManager));
    g_monitorManager.notifyWindow = hwnd;

    // Get available drives
    DWORD drives = GetLogicalDrives();
    int driveIndex = 0;

    for (int i = 0; i < 26 && driveIndex < MAX_DRIVES; i++)
    {
        if (drives & (1 << i))
        {
            WCHAR driveLetter = L'A' + i;

            if (IsValidDriveForMonitoring(driveLetter))
            {
                DriveMonitor *monitor = &g_monitorManager.drives[driveIndex];
                monitor->driveLetter = driveLetter;
                monitor->isActive = FALSE;
                monitor->shouldStop = FALSE;
                monitor->hDirectory = INVALID_HANDLE_VALUE;
                monitor->hThread = NULL;

                driveIndex++;
            }
        }
    }

    g_monitorManager.activeDriveCount = driveIndex;
    g_monitorManager.isInitialized = TRUE;

    printf("File monitor initialized for %d drives\n", driveIndex);
    return TRUE;
}

// Check if drive is valid for monitoring (NTFS drives)
BOOL IsValidDriveForMonitoring(WCHAR driveLetter)
{
    WCHAR rootPath[4] = {driveLetter, L':', L'\\', L'\0'};
    WCHAR fileSystem[32];

    if (GetVolumeInformationW(rootPath, NULL, 0, NULL, NULL, NULL,
                              fileSystem, sizeof(fileSystem) / sizeof(WCHAR)))
    {
        return (wcscmp(fileSystem, L"NTFS") == 0);
    }

    return FALSE;
}

// Start monitoring all drives
BOOL StartMonitoring()
{
    if (!g_monitorManager.isInitialized || g_monitorManager.isMonitoring)
        return FALSE;

    printf("Starting file monitoring...\n");

    for (int i = 0; i < g_monitorManager.activeDriveCount; i++)
    {
        if (StartDriveMonitoring(&g_monitorManager.drives[i]))
        {
            printf("Started monitoring drive %c:\n", (char)g_monitorManager.drives[i].driveLetter);
        }
        else
        {
            printf("Failed to start monitoring drive %c:\n", (char)g_monitorManager.drives[i].driveLetter);
        }
    }

    g_monitorManager.isMonitoring = TRUE;
    return TRUE;
}

// Start monitoring a specific drive
BOOL StartDriveMonitoring(DriveMonitor *monitor)
{
    if (!monitor || monitor->isActive)
        return FALSE;

    // Open directory handle
    WCHAR drivePath[4] = {monitor->driveLetter, L':', L'\\', L'\0'};

    monitor->hDirectory = CreateFileW(
        drivePath,
        FILE_LIST_DIRECTORY,
        FILE_SHARE_READ | FILE_SHARE_WRITE | FILE_SHARE_DELETE,
        NULL,
        OPEN_EXISTING,
        FILE_FLAG_BACKUP_SEMANTICS | FILE_FLAG_OVERLAPPED,
        NULL);

    if (monitor->hDirectory == INVALID_HANDLE_VALUE)
    {
        printf("Failed to open directory for drive %c: Error %lu\n",
               (char)monitor->driveLetter, GetLastError());
        return FALSE;
    }

    // Create monitoring thread
    monitor->hThread = CreateThread(NULL, 0, DriveMonitorThread, monitor, 0, NULL);
    if (!monitor->hThread)
    {
        CloseHandle(monitor->hDirectory);
        monitor->hDirectory = INVALID_HANDLE_VALUE;
        return FALSE;
    }

    monitor->isActive = TRUE;
    return TRUE;
}

// Drive monitoring thread
DWORD WINAPI DriveMonitorThread(LPVOID lpParam)
{
    DriveMonitor *monitor = (DriveMonitor *)lpParam;
    DWORD bytesReturned;

    while (!monitor->shouldStop)
    {
        // Initialize overlapped structure
        memset(&monitor->overlapped, 0, sizeof(OVERLAPPED));
        monitor->overlapped.hEvent = CreateEvent(NULL, TRUE, FALSE, NULL);

        // Start asynchronous directory monitoring
        BOOL result = ReadDirectoryChangesW(
            monitor->hDirectory,
            monitor->buffer,
            BUFFER_SIZE,
            TRUE, // Watch subdirectories
            MONITOR_FLAGS,
            &bytesReturned,
            &monitor->overlapped,
            NULL);

        if (!result)
        {
            printf("ReadDirectoryChangesW failed for drive %c: Error %lu\n",
                   (char)monitor->driveLetter, GetLastError());
            break;
        }

        // Wait for changes or stop signal
        DWORD waitResult = WaitForSingleObject(monitor->overlapped.hEvent, 1000);

        if (waitResult == WAIT_OBJECT_0)
        {
            // Get the actual number of bytes returned
            if (GetOverlappedResult(monitor->hDirectory, &monitor->overlapped, &bytesReturned, FALSE))
            {
                // Process file changes
                ProcessDirectoryChanges(monitor, monitor->buffer, bytesReturned);
            }
        }
        else if (waitResult == WAIT_TIMEOUT)
        {
            // Timeout - continue monitoring
            CancelIo(monitor->hDirectory);
        }

        CloseHandle(monitor->overlapped.hEvent);
    }

    monitor->isActive = FALSE;
    return 0;
}

// Process directory changes
void ProcessDirectoryChanges(DriveMonitor *monitor, BYTE *buffer, DWORD bufferSize)
{
    DWORD offset = 0;

    while (offset < bufferSize)
    {
        FILE_NOTIFY_INFORMATION *info = (FILE_NOTIFY_INFORMATION *)(buffer + offset);

        // Create file change event
        FileChangeEvent event = {0};
        event.action = info->Action;

        // Copy filename (convert from WCHAR to null-terminated)
        DWORD nameLength = info->FileNameLength / sizeof(WCHAR);
        if (nameLength >= MAX_FILENAME_LENGTH)
            nameLength = MAX_FILENAME_LENGTH - 1;

        wcsncpy(event.fileName, info->FileName, nameLength);
        event.fileName[nameLength] = L'\0';

        // Build full path
        swprintf(event.fullPath, MAX_PATH, L"%c:\\%s",
                 (char)monitor->driveLetter, event.fileName);

        // Get file information if file exists
        if (event.action != FILE_ACTION_REMOVED)
        {
            GetFileInformation(event.fullPath, &event.fileSize, &event.lastWriteTime);
        }

        // Process the event
        ProcessFileChangeEvent(&event);

        // Move to next record
        if (info->NextEntryOffset == 0)
            break;
        offset += info->NextEntryOffset;
    }
}

// Process individual file change event
void ProcessFileChangeEvent(const FileChangeEvent *event)
{
    char fileName[MAX_FILENAME_LENGTH];
    char fullPath[MAX_PATH];
    char timeString[64];

    // Convert to UTF-8
    WideCharToMultiByte(CP_UTF8, 0, event->fileName, -1, fileName, sizeof(fileName), NULL, NULL);
    WideCharToMultiByte(CP_UTF8, 0, event->fullPath, -1, fullPath, sizeof(fullPath), NULL, NULL);
    ConvertFileTimeToString(&event->lastWriteTime, timeString, sizeof(timeString));

    // Update database based on action
    switch (event->action)
    {
    case FILE_ACTION_ADDED:
        printf("File added: %s\n", fullPath);
        AddFileToDatabase(event->fileName, event->fullPath, &event->fileSize, &event->lastWriteTime);
        if (g_monitorManager.notifyWindow)
            PostMessage(g_monitorManager.notifyWindow, WM_FILE_ADDED, 0, 0);
        break;

    case FILE_ACTION_REMOVED:
        printf("File removed: %s\n", fullPath);
        RemoveFileFromDatabase(event->fullPath);
        if (g_monitorManager.notifyWindow)
            PostMessage(g_monitorManager.notifyWindow, WM_FILE_REMOVED, 0, 0);
        break;

    case FILE_ACTION_MODIFIED:
        printf("File modified: %s\n", fullPath);
        UpdateFileInDatabase(event->fullPath, &event->fileSize, &event->lastWriteTime);
        if (g_monitorManager.notifyWindow)
            PostMessage(g_monitorManager.notifyWindow, WM_FILE_MODIFIED, 0, 0);
        break;

    case FILE_ACTION_RENAMED_OLD_NAME:
        // Store old name for rename operation
        // (In a real implementation, you'd need to handle the pair of old/new names)
        printf("File renamed from: %s\n", fullPath);
        break;

    case FILE_ACTION_RENAMED_NEW_NAME:
        printf("File renamed to: %s\n", fullPath);
        // Update database with new name
        AddFileToDatabase(event->fileName, event->fullPath, &event->fileSize, &event->lastWriteTime);
        if (g_monitorManager.notifyWindow)
            PostMessage(g_monitorManager.notifyWindow, WM_FILE_RENAMED, 0, 0);
        break;
    }
}

// Get file information
BOOL GetFileInformation(const WCHAR *filePath, LARGE_INTEGER *fileSize, FILETIME *lastWriteTime)
{
    WIN32_FILE_ATTRIBUTE_DATA fileData;

    if (GetFileAttributesExW(filePath, GetFileExInfoStandard, &fileData))
    {
        fileSize->LowPart = fileData.nFileSizeLow;
        fileSize->HighPart = fileData.nFileSizeHigh;
        *lastWriteTime = fileData.ftLastWriteTime;
        return TRUE;
    }

    // Set defaults if file info cannot be retrieved
    fileSize->QuadPart = 0;
    memset(lastWriteTime, 0, sizeof(FILETIME));
    return FALSE;
}

// Convert FILETIME to string
void ConvertFileTimeToString(const FILETIME *fileTime, char *timeString, size_t bufferSize)
{
    SYSTEMTIME st;

    if (FileTimeToSystemTime(fileTime, &st))
    {
        snprintf(timeString, bufferSize, "%04d-%02d-%02d %02d:%02d:%02d",
                 st.wYear, st.wMonth, st.wDay, st.wHour, st.wMinute, st.wSecond);
    }
    else
    {
        strcpy(timeString, "Unknown");
    }
}

// Database operations for file monitoring
BOOL AddFileToDatabase(const WCHAR *fileName, const WCHAR *fullPath,
                       const LARGE_INTEGER *fileSize, const FILETIME *lastWriteTime)
{
    if (!g_database)
        return FALSE;

    char name[MAX_FILENAME_LENGTH];
    char path[MAX_PATH];
    char timeString[64];

    WideCharToMultiByte(CP_UTF8, 0, fileName, -1, name, sizeof(name), NULL, NULL);
    WideCharToMultiByte(CP_UTF8, 0, fullPath, -1, path, sizeof(path), NULL, NULL);
    ConvertFileTimeToString(lastWriteTime, timeString, sizeof(timeString));

    const char *sql = "INSERT OR REPLACE INTO files (name, path, size, modified) VALUES (?, ?, ?, ?);";
    sqlite3_stmt *stmt;

    int rc = sqlite3_prepare_v2(g_database, sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK)
        return FALSE;

    sqlite3_bind_text(stmt, 1, name, -1, SQLITE_STATIC);
    sqlite3_bind_text(stmt, 2, path, -1, SQLITE_STATIC);
    sqlite3_bind_int64(stmt, 3, fileSize->QuadPart);
    sqlite3_bind_text(stmt, 4, timeString, -1, SQLITE_STATIC);

    rc = sqlite3_step(stmt);
    sqlite3_finalize(stmt);

    return (rc == SQLITE_DONE);
}

BOOL RemoveFileFromDatabase(const WCHAR *fullPath)
{
    if (!g_database)
        return FALSE;

    char path[MAX_PATH];
    WideCharToMultiByte(CP_UTF8, 0, fullPath, -1, path, sizeof(path), NULL, NULL);

    const char *sql = "DELETE FROM files WHERE path = ?;";
    sqlite3_stmt *stmt;

    int rc = sqlite3_prepare_v2(g_database, sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK)
        return FALSE;

    sqlite3_bind_text(stmt, 1, path, -1, SQLITE_STATIC);

    rc = sqlite3_step(stmt);
    sqlite3_finalize(stmt);

    return (rc == SQLITE_DONE);
}

BOOL UpdateFileInDatabase(const WCHAR *fullPath, const LARGE_INTEGER *fileSize,
                          const FILETIME *lastWriteTime)
{
    if (!g_database)
        return FALSE;

    char path[MAX_PATH];
    char timeString[64];

    WideCharToMultiByte(CP_UTF8, 0, fullPath, -1, path, sizeof(path), NULL, NULL);
    ConvertFileTimeToString(lastWriteTime, timeString, sizeof(timeString));

    const char *sql = "UPDATE files SET size = ?, modified = ? WHERE path = ?;";
    sqlite3_stmt *stmt;

    int rc = sqlite3_prepare_v2(g_database, sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK)
        return FALSE;

    sqlite3_bind_int64(stmt, 1, fileSize->QuadPart);
    sqlite3_bind_text(stmt, 2, timeString, -1, SQLITE_STATIC);
    sqlite3_bind_text(stmt, 3, path, -1, SQLITE_STATIC);

    rc = sqlite3_step(stmt);
    sqlite3_finalize(stmt);

    return (rc == SQLITE_DONE);
}

// Configuration functions
BOOL ShouldPerformFullScan()
{
    // Check if this is the first run or if full scan is needed
    DWORD fileAttr = GetFileAttributes("file_index.db");
    if (fileAttr == INVALID_FILE_ATTRIBUTES)
    {
        return TRUE; // Database doesn't exist, need full scan
    }

    // Check if scan_completed flag exists in config
    char scanCompleted[10];
    GetPrivateProfileString("Indexing", "ScanCompleted", "0", scanCompleted, sizeof(scanCompleted), ".\\config.ini");

    return (strcmp(scanCompleted, "1") != 0);
}

void SetFullScanCompleted()
{
    WritePrivateProfileString("Indexing", "ScanCompleted", "1", ".\\config.ini");
    WritePrivateProfileString(NULL, NULL, NULL, ".\\config.ini");
}

BOOL IsFirstRun()
{
    return ShouldPerformFullScan();
}

// Stop monitoring
BOOL StopMonitoring()
{
    if (!g_monitorManager.isMonitoring)
        return TRUE;

    printf("Stopping file monitoring...\n");

    for (int i = 0; i < g_monitorManager.activeDriveCount; i++)
    {
        StopDriveMonitoring(&g_monitorManager.drives[i]);
    }

    g_monitorManager.isMonitoring = FALSE;
    return TRUE;
}

BOOL StopDriveMonitoring(DriveMonitor *monitor)
{
    if (!monitor || !monitor->isActive)
        return TRUE;

    monitor->shouldStop = TRUE;

    if (monitor->hThread)
    {
        WaitForSingleObject(monitor->hThread, 5000);
        CloseHandle(monitor->hThread);
        monitor->hThread = NULL;
    }

    if (monitor->hDirectory != INVALID_HANDLE_VALUE)
    {
        CloseHandle(monitor->hDirectory);
        monitor->hDirectory = INVALID_HANDLE_VALUE;
    }

    monitor->isActive = FALSE;
    return TRUE;
}

void CleanupFileMonitor()
{
    StopMonitoring();
    memset(&g_monitorManager, 0, sizeof(FileMonitorManager));
}
