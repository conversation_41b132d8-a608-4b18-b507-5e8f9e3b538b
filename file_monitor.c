#include "file_monitor.h"

// ============================================================================
// 未使用的函数（占位符）
// ============================================================================

BOOL InitializeFileMonitor(HWND hwnd) { return FALSE; }
BOOL StartMonitoring() { return FALSE; }
BOOL StartDriveMonitoring(DriveMonitor *monitor) { return FALSE; }
DWORD WINAPI DriveMonitorThread(LPVOID lpParam) { return 0; }
BOOL GetFileInformation(const WCHAR *filePath, LARGE_INTEGER *fileSize, FILETIME *lastWriteTime) { return FALSE; }
BOOL IsValidDriveForMonitoring(WCHAR driveLetter) { return FALSE; }

BOOL AddFileToDatabase(const WCHAR *fileName, const WCHAR *fullPath, const LARGE_INTEGER *fileSize, const FILETIME *lastWriteTime) { return TRUE; }
BOOL RemoveFileFromDatabase(const WCHAR *fullPath) { return TRUE; }
BOOL UpdateFileInDatabase(const WCHAR *fullPath, const LARGE_INTEGER *fileSize, const FILETIME *lastWriteTime) { return TRUE; }
BOOL RenameFileInDatabase(const WCHAR *oldPath, const WCHAR *newPath) { return TRUE; }
BOOL ShouldPerformFullScan() { return FALSE; }
void SetFullScanCompleted() {}
BOOL StopMonitoring() { return TRUE; }
BOOL StopDriveMonitoring(DriveMonitor *monitor) { return TRUE; }
void CleanupFileMonitor() {}
