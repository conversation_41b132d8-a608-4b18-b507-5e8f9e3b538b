#ifndef UNICODE
#define UNICODE
#endif
#ifndef _UNICODE
#define _UNICODE
#endif
#include <windows.h>
#include <commctrl.h>
#include <stdio.h>
#include <locale.h>
#include <string.h>
#include <wchar.h>
#include <time.h>
#include <shlwapi.h>
#include <shellapi.h>
#include <commdlg.h>
// SQLite3 removed
#include "usn_journal.h"

// DPI awareness constants and types (for compatibility)
#ifndef DPI_AWARENESS_CONTEXT
typedef HANDLE DPI_AWARENESS_CONTEXT;
#endif

#ifndef DPI_AWARENESS_CONTEXT_PER_MONITOR_AWARE_V2
#define DPI_AWARENESS_CONTEXT_PER_MONITOR_AWARE_V2 ((DPI_AWARENESS_CONTEXT) - 4)
#endif

#ifndef PROCESS_PER_MONITOR_DPI_AWARE
typedef enum PROCESS_DPI_AWARENESS
{
    PROCESS_DPI_UNAWARE = 0,
    PROCESS_SYSTEM_DPI_AWARE = 1,
    PROCESS_PER_MONITOR_DPI_AWARE = 2
} PROCESS_DPI_AWARENESS;
#endif

#ifndef WM_DPICHANGED
#define WM_DPICHANGED 0x02E0
#endif

// Function declarations for DPI awareness (for compatibility)
#ifndef DECLSPEC_IMPORT
#define DECLSPEC_IMPORT __declspec(dllimport)
#endif

DECLSPEC_IMPORT BOOL WINAPI SetProcessDPIAware(void);

// 资源定义
#define IDI_MAIN_ICON 101

// 右键菜单ID定义
#define ID_OPEN_FOLDER 1001
#define ID_OPEN_FILE 1002
#define ID_COPY_PATH 1003
#define ID_PROPERTIES 1004

// 日志宏已删除

// ...全局变量和所有函数实现...

#pragma comment(lib, "comctl32.lib")
#pragma comment(lib, "gdi32.lib")
#pragma comment(lib, "user32.lib")
#pragma comment(lib, "shlwapi.lib")
#pragma comment(lib, "advapi32.lib")

// Constants for flat design
#define DEFAULT_WINDOW_WIDTH 1200
#define DEFAULT_WINDOW_HEIGHT 800
#define SPLITTER_WIDTH 1      // 1px splitter
#define FONT_SIZE 20          // 26pt font
#define LINE_HEIGHT 34        // 30px line height
#define BORDER_WIDTH 0        // 0px gaps between windows
#define INPUT_HEIGHT 30       // Input box height
#define STATUS_HEIGHT 30      // Status bar height
#define MAX_FILTER_LENGTH 256 // Maximum filter text length
#define CONFIG_FILE "config.ini"
#define SCAN_STATUS_FILE "scan_status.ini"

// Colors for flat design
#define FLAT_COLOR_BACKGROUND RGB(240, 240, 240)
#define FLAT_COLOR_BORDER RGB(200, 200, 200)
#define FLAT_COLOR_SPLITTER RGB(180, 180, 180)
#define FLAT_COLOR_TEXT RGB(50, 50, 50)

// File data structure for caching (Unicode support)
typedef struct
{
    WCHAR name[MAX_PATH];
    WCHAR path[MAX_PATH * 2];
    WCHAR size[64];
    WCHAR modified[64];
    WCHAR fullPath[MAX_PATH * 3]; // For file preview
} FileData;

// Configuration structure (窗口和UI设置)
typedef struct
{
    int windowX, windowY;
    int windowWidth, windowHeight;
    BOOL windowMaximized;
    int splitterPosition;

    // ListView column settings
    int columnWidths[4]; // 列宽度：名称、路径、大小、修改时间
    int columnOrder[4];  // 列顺序：0=名称, 1=路径, 2=大小, 3=修改时间
} ConfigData;

// USN file data structure removed (defined below)

// Global variables
HWND hwndMain = NULL;
HWND hwndStatus = NULL;
HWND hwndLeft = NULL;
HWND hwndRight = NULL;
HWND hwndEdit = NULL;
HFONT hFont = NULL;

// Virtual ListView architecture
static BOOL g_dataLoaded = FALSE;
static BOOL g_mainWindowReady = FALSE;
static BOOL g_useVirtualMode = TRUE;
static CRITICAL_SECTION g_dataCriticalSection;

// Virtual ListView data
static int g_virtualItemCount = 0;
static BOOL g_dataLoadInProgress = FALSE;

// Virtual ListView filtering - 使用位图优化内存
static BYTE *g_filterBitmap = NULL; // 筛选位图，每个文件1位
static int g_filteredCount = 0;     // 筛选后的数量
static BOOL g_filterActive = FALSE; // 是否有活动筛选
static int g_bitmapSize = 0;        // 位图大小（字节数）

// Data reload functionality
static UINT_PTR g_reloadTimerId = 0;
static BOOL g_autoReloadEnabled = TRUE;
HBRUSH hBackgroundBrush = NULL;
HBRUSH hSplitterBrush = NULL;
int splitterPos = 400;
BOOL isDragging = FALSE;

// 阶段1优化：进一步压缩的USN内存数据结构
typedef struct
{
    WCHAR fileName[32];  // 64字节 = 32个WCHAR字符 (减少8字节)
    WCHAR fullPath[64];  // 128字节 = 64个WCHAR字符 (减少32字节)
    DWORD fileSize;      // 32位，支持4GB文件
    DWORD lastWriteTime; // 32位时间戳
    BYTE isDirectory;    // 1字节：1=文件夹，0=文件
    // 移除预计算的字符串，改为动态生成以节省内存
    // sizeStr和timeStr在显示时动态计算
} USNFileData;

// USN Memory variables
USNFileData *g_usnFiles = NULL;
int g_usnFileCount = 0;
int g_usnFileCapacity = 0;
CRITICAL_SECTION g_usnDataCriticalSection;

// Filtering variables
WCHAR g_currentFilter[MAX_FILTER_LENGTH] = L"";

// 筛选缓存优化
WCHAR g_lastFilterText[MAX_FILTER_LENGTH] = L"";
BOOL g_filterCacheValid = FALSE;

// 筛选性能统计
DWORD g_totalFilterTime = 0;
int g_filterOperationCount = 0;

// DPI 自适应相关变量
static int g_dpiX = 96;          // 当前DPI X
static int g_dpiY = 96;          // 当前DPI Y
static float g_dpiScaleX = 1.0f; // DPI缩放比例 X
static float g_dpiScaleY = 1.0f; // DPI缩放比例 Y
static BOOL g_dpiAware = FALSE;  // DPI感知状态

// 排序相关变量
int g_sortColumn = -1;         // 当前排序列，-1表示未排序
BOOL g_sortAscending = TRUE;   // TRUE=升序，FALSE=降序
HANDLE g_sortThread = NULL;    // 排序线程句柄
BOOL g_sortInProgress = FALSE; // 排序进行中标志

// 筛选线程相关变量
HANDLE g_filterThread = NULL;
DWORD g_filterThreadId = 0;
CRITICAL_SECTION g_filterCriticalSection;
BOOL g_filterThreadRunning = FALSE;
BOOL g_filterPending = FALSE;
WCHAR g_pendingFilterText[256] = L"";

// Configuration variables
ConfigData g_config = {0};
HANDLE g_fileWatchThread = NULL;
HANDLE g_usnReadThread = NULL;
BOOL g_fileWatchActive = FALSE;
BOOL g_usnReadInProgress = FALSE;
BOOL g_programInitialized = FALSE; // 标记程序是否完全初始化完成

// Function declarations
LRESULT CALLBACK WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam);
void CreateControls(HWND hwnd);
void UpdateLayout(HWND hwnd);
void CreateFlatFont();
void DrawSplitter(HDC hdc, RECT *rect);

// Database functions (removed - no longer using database)

// DPI awareness functions
void InitializeDPIAwareness();
void UpdateDPISettings(HWND hwnd);
int ScaleDPIX(int value);
int ScaleDPIY(int value);
HFONT CreateScaledFont(int baseSize, const WCHAR *fontName);
void HandleDPIChanged(HWND hwnd, WPARAM wParam, LPARAM lParam);

// Filtering functions
void ApplyFilter(const WCHAR *filterText);
BOOL MatchesFilter(const WCHAR *filename, const WCHAR *path, const WCHAR *filter);
BOOL MatchesSingleKeyword(const WCHAR *filename, const WCHAR *keyword);
BOOL MatchesMultipleKeywords(const WCHAR *filename, const WCHAR *filter);
WCHAR *FindSubstringIgnoreCase(const WCHAR *haystack, const WCHAR *needle);
void UpdateStatusBar();
void OnFilterTextChanged();

// Virtual ListView functions
LRESULT HandleVirtualListViewNotify(NMLVDISPINFOW *pDispInfo);

// USN file tree rendering functions
void InitializeUSNMemory();
void CleanupUSNMemory();
void AddUSNFile(const WCHAR *fileName, const WCHAR *fullPath, LONGLONG fileSize, FILETIME lastWriteTime, DWORD fileAttributes);

// 筛选线程函数
DWORD WINAPI FilterThreadProc(LPVOID lpParam);

// 排序函数
int CompareUSNFiles(const void *a, const void *b);
void SortFileList(int column);
void ReverseFileList();
void SortFilteredResults(int column);
DWORD WINAPI SortThreadProc(LPVOID lpParam);
void InitializeFilterThread();
void CleanupFilterThread();
void RequestFilter(const WCHAR *filterText);
void PerformFilterOperation(const WCHAR *filterText);
void PerformBatchFiltering(const WCHAR *filterText);

// 位图筛选辅助函数
void InitializeFilterBitmap();
void CleanupFilterBitmap();
void SetFilterBit(int index, BOOL value);
BOOL GetFilterBit(int index);
int GetFilteredIndexByPosition(int position);

// 右键菜单功能函数
HMENU CreateFileContextMenu();
void HandleListViewRightClick(LPNMITEMACTIVATE pnmia);
int GetActualFileIndex(int selectedIndex);
void BuildFullFilePath(int actualIndex, WCHAR *fullPath);
void ShowContextMenu(POINT pt, const WCHAR *filePath);
void HandleMenuCommand(int cmd, const WCHAR *filePath);
void OpenFileFolder(const WCHAR *filePath);
void OpenFileWithDefaultApp(const WCHAR *filePath);
void CopyPathToClipboard(const WCHAR *filePath);
void ShowFileProperties(const WCHAR *filePath);
int ReadUSNToMemory();
void RenderUSNFileTree();
void EnableRealTimeFiltering();

// Configuration functions
void LoadConfiguration();
void SaveConfiguration();
void ApplyConfiguration();
void CreateDefaultConfigFile();
void SaveColumnSettings();

// Scan status functions removed
// Database functions removed

// USN Journal functions
DWORD WINAPI USNReadThreadProc(LPVOID lpParam);
void StartUSNReadThread();

// Delayed database loading thread
DWORD WINAPI DelayedDatabaseLoadingThreadProc(LPVOID lpParam);
void StartDelayedDatabaseLoading();
// USN scan functions removed

// File monitoring functions
DWORD WINAPI FileWatchThreadProc(LPVOID lpParam);
void StartFileWatcher();
void StopFileWatcher();

// File preview functions
void PreviewSelectedFile();
void LoadFilePreview(const WCHAR *filePath);
BOOL IsTextFile(const WCHAR *filePath);
BOOL IsImageFile(const WCHAR *filePath);

// Utility functions
void SetControlTextUTF8(HWND hwnd, const WCHAR *text);

// Set control text with Unicode support
void SetControlTextUTF8(HWND hwnd, const WCHAR *text)
{
    if (!hwnd || !text)
        return;
    SetWindowTextW(hwnd, text);
}

// 优化的筛选匹配函数 - 减少字符串操作开销
BOOL MatchesFilter(const WCHAR *filename, const WCHAR *path, const WCHAR *filter)
{
    if (!filter || wcslen(filter) == 0)
        return TRUE;

    // 快速单关键词匹配（大多数情况）
    if (wcschr(filter, L' ') == NULL)
    {
        return MatchesSingleKeyword(filename, filter);
    }

    // 多关键词匹配
    return MatchesMultipleKeywords(filename, filter);
}

// 单关键词快速匹配（避免字符串复制）
BOOL MatchesSingleKeyword(const WCHAR *filename, const WCHAR *keyword)
{
    if (!filename || !keyword)
        return FALSE;

    // 使用优化的不区分大小写搜索
    return FindSubstringIgnoreCase(filename, keyword) != NULL;
}

// 多关键词匹配
BOOL MatchesMultipleKeywords(const WCHAR *filename, const WCHAR *filter)
{
    WCHAR filterCopy[MAX_FILTER_LENGTH];
    wcscpy(filterCopy, filter);

    WCHAR *token;
    WCHAR *context = NULL;
    token = wcstok_s(filterCopy, L" ", &context);

    while (token != NULL)
    {
        // 跳过空token
        if (wcslen(token) == 0)
        {
            token = wcstok_s(NULL, L" ", &context);
            continue;
        }

        // 检查当前关键词是否在文件名中
        if (FindSubstringIgnoreCase(filename, token) == NULL)
        {
            return FALSE;
        }

        token = wcstok_s(NULL, L" ", &context);
    }

    return TRUE;
}

// 优化的不区分大小写子串查找（避免完整字符串转换）
WCHAR *FindSubstringIgnoreCase(const WCHAR *haystack, const WCHAR *needle)
{
    if (!haystack || !needle)
        return NULL;

    int needleLen = wcslen(needle);
    if (needleLen == 0)
        return (WCHAR *)haystack;

    int haystackLen = wcslen(haystack);
    if (needleLen > haystackLen)
        return NULL;

    // 逐字符比较，避免完整字符串转换
    for (int i = 0; i <= haystackLen - needleLen; i++)
    {
        BOOL match = TRUE;
        for (int j = 0; j < needleLen; j++)
        {
            WCHAR c1 = haystack[i + j];
            WCHAR c2 = needle[j];

            // 转换为小写进行比较
            if (c1 >= L'A' && c1 <= L'Z')
                c1 += 32;
            if (c2 >= L'A' && c2 <= L'Z')
                c2 += 32;

            if (c1 != c2)
            {
                match = FALSE;
                break;
            }
        }

        if (match)
            return (WCHAR *)(haystack + i);
    }

    return NULL;
}

// ==================== DPI 自适应功能实现 ====================

// 初始化DPI感知
void InitializeDPIAwareness()
{
    // 尝试设置Per-Monitor DPI Awareness V2 (Windows 10 1703+)
    typedef HRESULT(WINAPI * SetProcessDpiAwarenessContextProc)(DPI_AWARENESS_CONTEXT);
    HMODULE hUser32 = GetModuleHandleW(L"user32.dll");

    if (hUser32)
    {
        SetProcessDpiAwarenessContextProc SetProcessDpiAwarenessContextFunc =
            (SetProcessDpiAwarenessContextProc)GetProcAddress(hUser32, "SetProcessDpiAwarenessContext");

        if (SetProcessDpiAwarenessContextFunc)
        {
            HRESULT hr = SetProcessDpiAwarenessContextFunc(DPI_AWARENESS_CONTEXT_PER_MONITOR_AWARE_V2);
            if (SUCCEEDED(hr))
            {
                g_dpiAware = TRUE;
                return;
            }
        }
    }

    // 回退到Per-Monitor DPI Awareness V1 (Windows 8.1+)
    typedef HRESULT(WINAPI * SetProcessDpiAwarenessProc)(PROCESS_DPI_AWARENESS);
    HMODULE hShcore = LoadLibraryW(L"shcore.dll");

    if (hShcore)
    {
        SetProcessDpiAwarenessProc SetProcessDpiAwarenessFunc =
            (SetProcessDpiAwarenessProc)GetProcAddress(hShcore, "SetProcessDpiAwareness");

        if (SetProcessDpiAwarenessFunc)
        {
            HRESULT hr = SetProcessDpiAwarenessFunc(PROCESS_PER_MONITOR_DPI_AWARE);
            if (SUCCEEDED(hr))
            {
                g_dpiAware = TRUE;
                FreeLibrary(hShcore);
                return;
            }
        }
        FreeLibrary(hShcore);
    }

    // 最后回退到系统DPI感知 (Windows Vista+)
    if (SetProcessDPIAware())
    {
        g_dpiAware = TRUE;
    }
}

// 更新DPI设置
void UpdateDPISettings(HWND hwnd)
{
    if (!hwnd)
        return;

    // 尝试获取窗口的DPI (Windows 10 1607+)
    typedef UINT(WINAPI * GetDpiForWindowProc)(HWND);
    HMODULE hUser32 = GetModuleHandleW(L"user32.dll");

    if (hUser32)
    {
        GetDpiForWindowProc GetDpiForWindowFunc =
            (GetDpiForWindowProc)GetProcAddress(hUser32, "GetDpiForWindow");

        if (GetDpiForWindowFunc)
        {
            UINT dpi = GetDpiForWindowFunc(hwnd);
            g_dpiX = g_dpiY = dpi;
            g_dpiScaleX = g_dpiScaleY = (float)dpi / 96.0f;
            return;
        }
    }

    // 回退到系统DPI
    HDC hdc = GetDC(hwnd);
    if (hdc)
    {
        g_dpiX = GetDeviceCaps(hdc, LOGPIXELSX);
        g_dpiY = GetDeviceCaps(hdc, LOGPIXELSY);
        g_dpiScaleX = (float)g_dpiX / 96.0f;
        g_dpiScaleY = (float)g_dpiY / 96.0f;
        ReleaseDC(hwnd, hdc);
    }
}

// DPI缩放X坐标
int ScaleDPIX(int value)
{
    return (int)(value * g_dpiScaleX + 0.5f);
}

// DPI缩放Y坐标
int ScaleDPIY(int value)
{
    return (int)(value * g_dpiScaleY + 0.5f);
}

// 创建DPI缩放的字体
HFONT CreateScaledFont(int baseSize, const WCHAR *fontName)
{
    int scaledSize = ScaleDPIY(baseSize);

    LOGFONTW lf = {0};
    lf.lfHeight = -scaledSize;
    lf.lfWeight = FW_NORMAL;
    lf.lfCharSet = DEFAULT_CHARSET;
    lf.lfOutPrecision = OUT_DEFAULT_PRECIS;
    lf.lfClipPrecision = CLIP_DEFAULT_PRECIS;
    lf.lfQuality = CLEARTYPE_QUALITY;
    lf.lfPitchAndFamily = DEFAULT_PITCH | FF_DONTCARE;

    if (fontName)
    {
        wcsncpy(lf.lfFaceName, fontName, LF_FACESIZE - 1);
    }
    else
    {
        wcscpy(lf.lfFaceName, L"Microsoft YaHei Light");
    }

    return CreateFontIndirectW(&lf);
}

// 处理DPI变化消息
void HandleDPIChanged(HWND hwnd, WPARAM wParam, LPARAM lParam)
{
    // 获取新的DPI值
    UINT newDpiX = LOWORD(wParam);
    UINT newDpiY = HIWORD(wParam);

    // 更新DPI设置
    g_dpiX = newDpiX;
    g_dpiY = newDpiY;
    g_dpiScaleX = (float)newDpiX / 96.0f;
    g_dpiScaleY = (float)newDpiY / 96.0f;

    // 获取建议的窗口矩形
    RECT *prcNewWindow = (RECT *)lParam;
    if (prcNewWindow)
    {
        SetWindowPos(hwnd, NULL,
                     prcNewWindow->left, prcNewWindow->top,
                     prcNewWindow->right - prcNewWindow->left,
                     prcNewWindow->bottom - prcNewWindow->top,
                     SWP_NOZORDER | SWP_NOACTIVATE);
    }

    // 重新创建控件和字体
    PostMessage(hwnd, WM_USER + 10, 0, 0); // 自定义消息：重新布局
}

// 批量筛选优化函数
void PerformBatchFiltering(const WCHAR *filterText)
{
    if (!g_usnFiles || g_usnFileCount == 0 || !g_filterBitmap)
        return;

    DWORD startTime = GetTickCount();
    g_filteredCount = 0;

    // 批量处理大小（优化内存访问模式）
    const int BATCH_SIZE = 1024;

    // 预先分析筛选条件，优化匹配策略
    BOOL isSimpleFilter = (wcschr(filterText, L' ') == NULL);
    int filterLen = wcslen(filterText);

    for (int batchStart = 0; batchStart < g_usnFileCount; batchStart += BATCH_SIZE)
    {
        int batchEnd = min(batchStart + BATCH_SIZE, g_usnFileCount);

        // 批量处理当前批次
        for (int i = batchStart; i < batchEnd; i++)
        {
            BOOL matches;

            // 根据筛选类型选择优化路径
            if (isSimpleFilter)
            {
                matches = MatchesSingleKeyword(g_usnFiles[i].fileName, filterText);
            }
            else
            {
                matches = MatchesFilter(g_usnFiles[i].fileName, g_usnFiles[i].fullPath, filterText);
            }

            if (matches)
            {
                SetFilterBit(i, TRUE);
                g_filteredCount++;
            }
            else
            {
                SetFilterBit(i, FALSE);
            }
        }

        // 每处理一个批次检查是否需要中断
        if (!g_filterThreadRunning)
        {
            break;
        }

        // 定期报告进度（每处理10个批次）
        if ((batchStart / BATCH_SIZE) % 10 == 0)
        {
            DWORD elapsed = GetTickCount() - startTime;
            double rate = (elapsed > 0) ? (double)batchEnd / (elapsed / 1000.0) : 0;
        }
    }

    DWORD totalTime = GetTickCount() - startTime;
    double rate = (totalTime > 0) ? (double)g_usnFileCount / (totalTime / 1000.0) : 0;
}

// Update status bar with current filter info
void UpdateStatusBar()
{
    if (!hwndStatus)
        return;

    WCHAR statusText[512];
    if (g_filterActive && wcslen(g_currentFilter) > 0)
    {
        swprintf(statusText, 512, L"筛选：\"%s\" - 显示 %d / %d 个文件",
                 g_currentFilter, g_filteredCount, g_usnFileCount);
    }
    else
    {
        swprintf(statusText, 512, L"文件预览器 - 共 %d 个文件", g_usnFileCount);
    }

    SendMessageW(hwndStatus, SB_SETTEXTW, 0, (LPARAM)statusText);
}

// Apply filter to Virtual ListView - USN memory-based filtering
void ApplyFilter(const WCHAR *filterText)
{
    // 检查数据加载状态和USN数据是否准备好
    if (!hwndLeft || !g_usnFiles || g_usnFileCount == 0 || !g_dataLoaded)
    {
        return;
    }

    // 使用线程化筛选，避免UI卡顿
    RequestFilter(filterText);
}

// Handle filter text change
void OnFilterTextChanged()
{
    // 早期检查：确保基本组件和数据已准备好
    if (!hwndEdit || !g_dataLoaded)
    {
        return;
    }

    WCHAR filterText[MAX_FILTER_LENGTH];
    GetWindowTextW(hwndEdit, filterText, MAX_FILTER_LENGTH);

    ApplyFilter(filterText);
}

// Render file tree from USN memory data
void RenderUSNFileTree()
{
    if (!hwndLeft || !g_usnFiles)
    {
        return;
    }

    if (!g_mainWindowReady)
    {
        return;
    }

    if (!IsWindow(hwndLeft))
    {
        return;
    }

    if (g_usnFileCount <= 0)
    {
        return;
    }

    // 使用虚拟模式设置项目数量
    EnterCriticalSection(&g_usnDataCriticalSection);

    ListView_SetItemCount(hwndLeft, g_usnFileCount);
    g_virtualItemCount = g_usnFileCount;
    g_dataLoaded = TRUE;

    LeaveCriticalSection(&g_usnDataCriticalSection);

    // 更新显示
    InvalidateRect(hwndLeft, NULL, FALSE);

    // Update status bar with final completion status
    if (hwndStatus)
    {
        WCHAR statusText[256];
        swprintf(statusText, 256, L"文件预览器 - 共 %d 个文件 (加载完成)", g_usnFileCount);
        SendMessageW(hwndStatus, SB_SETTEXTW, 0, (LPARAM)statusText);
    }

    EnableRealTimeFiltering();
}

// 删除了不需要的虚拟ListView辅助函数，简化架构

// Handle Virtual ListView data requests - with USN data
LRESULT HandleVirtualListViewNotify(NMLVDISPINFOW *pDispInfo)
{
    if (!pDispInfo || !g_usnFiles || !g_dataLoaded)
    {
        // 如果数据未准备好，返回空字符串
        if (pDispInfo && (pDispInfo->item.mask & LVIF_TEXT) && pDispInfo->item.pszText)
        {
            pDispInfo->item.pszText[0] = L'\0';
        }
        return 0;
    }

    int itemIndex = pDispInfo->item.iItem;

    // 使用TryEnterCriticalSection避免死锁
    if (!TryEnterCriticalSection(&g_dataCriticalSection))
    {
        // 如果无法获取锁，返回空字符串
        if (pDispInfo->item.mask & LVIF_TEXT && pDispInfo->item.pszText)
        {
            pDispInfo->item.pszText[0] = L'\0';
        }
        return 0;
    }

    // 获取实际的文件索引（考虑筛选）
    int actualIndex = itemIndex;
    if (g_filterActive && g_filterBitmap)
    {
        actualIndex = GetFilteredIndexByPosition(itemIndex);
        if (actualIndex == -1)
        {
            // 筛选模式下索引越界
            if (pDispInfo->item.mask & LVIF_TEXT && pDispInfo->item.pszText)
            {
                pDispInfo->item.pszText[0] = L'\0';
            }
            LeaveCriticalSection(&g_dataCriticalSection);
            return 0;
        }
    }

    // 安全检查：确保实际索引在有效范围内
    if (actualIndex >= 0 && actualIndex < g_usnFileCount && g_usnFiles)
    {
        if (pDispInfo->item.mask & LVIF_TEXT && pDispInfo->item.pszText && pDispInfo->item.cchTextMax > 0)
        {
            switch (pDispInfo->item.iSubItem)
            {
            case 0: // 文件名
                if (g_usnFiles[actualIndex].fileName[0] != L'\0')
                {
                    wcsncpy(pDispInfo->item.pszText, g_usnFiles[actualIndex].fileName,
                            pDispInfo->item.cchTextMax - 1);
                    pDispInfo->item.pszText[pDispInfo->item.cchTextMax - 1] = L'\0';
                }
                else
                {
                    wcscpy(pDispInfo->item.pszText, L"<No Name>");
                }
                break;
            case 1: // 路径
                if (g_usnFiles[actualIndex].fullPath[0] != L'\0')
                {
                    wcsncpy(pDispInfo->item.pszText, g_usnFiles[actualIndex].fullPath,
                            pDispInfo->item.cchTextMax - 1);
                    pDispInfo->item.pszText[pDispInfo->item.cchTextMax - 1] = L'\0';
                }
                else
                {
                    wcscpy(pDispInfo->item.pszText, L"<No Path>");
                }
                break;
            case 2: // 大小 - 动态生成字符串
            {
                WCHAR sizeStr[16];

                // 检查是否为文件夹
                if (g_usnFiles[actualIndex].isDirectory)
                {
                    wcscpy(sizeStr, L"folder");
                }
                else
                {
                    DWORD fileSize = g_usnFiles[actualIndex].fileSize;
                    if (fileSize < 1024)
                        swprintf(sizeStr, 16, L"%lu B", fileSize);
                    else if (fileSize < 1024 * 1024)
                        swprintf(sizeStr, 16, L"%.1fK", fileSize / 1024.0);
                    else if (fileSize < 1024UL * 1024 * 1024)
                        swprintf(sizeStr, 16, L"%.1fM", fileSize / (1024.0 * 1024.0));
                    else
                        swprintf(sizeStr, 16, L"%.1fG", fileSize / (1024.0 * 1024.0 * 1024.0));
                }

                wcsncpy(pDispInfo->item.pszText, sizeStr, pDispInfo->item.cchTextMax - 1);
                pDispInfo->item.pszText[pDispInfo->item.cchTextMax - 1] = L'\0';
            }
            break;
            case 3: // 修改时间 - 动态生成字符串
            {
                WCHAR timeStr[20];
                DWORD timestamp = g_usnFiles[actualIndex].lastWriteTime;
                if (timestamp > 0)
                {
                    // 直接使用Unix时间戳转换为本地时间
                    time_t fileTime = (time_t)timestamp;
                    struct tm *timeInfo = localtime(&fileTime);
                    if (timeInfo)
                    {
                        swprintf(timeStr, 20, L"%04d/%02d/%02d-%02d:%02d",
                                 timeInfo->tm_year + 1900, timeInfo->tm_mon + 1, timeInfo->tm_mday,
                                 timeInfo->tm_hour, timeInfo->tm_min);
                    }
                    else
                    {
                        wcscpy(timeStr, L"时间错误");
                    }
                }
                else
                {
                    wcscpy(timeStr, L"未知");
                }

                wcsncpy(pDispInfo->item.pszText, timeStr, pDispInfo->item.cchTextMax - 1);
                pDispInfo->item.pszText[pDispInfo->item.cchTextMax - 1] = L'\0';
            }
            break;
            default:
                wcscpy(pDispInfo->item.pszText, L"");
                break;
            }
            pDispInfo->item.pszText[pDispInfo->item.cchTextMax - 1] = L'\0';
        }
    }
    else
    {
        // 索引越界，返回空字符串
        if (pDispInfo->item.mask & LVIF_TEXT && pDispInfo->item.pszText)
        {
            pDispInfo->item.pszText[0] = L'\0';
        }
    }

    LeaveCriticalSection(&g_dataCriticalSection);
    return 0;
}

// 删除了UpdateVirtualListView函数，简化架构

// Enable real-time filtering - simplified
void EnableRealTimeFiltering()
{
    // 筛选功能通过OnFilterTextChanged实现
}

// Database functions removed - using USN memory only

// Create default configuration file
void CreateDefaultConfigFile()
{
    // 使用绝对路径来确保文件创建成功
    char fullPath[MAX_PATH];
    GetCurrentDirectoryA(MAX_PATH, fullPath);
    strcat(fullPath, "\\");
    strcat(fullPath, CONFIG_FILE);

    // Write default window settings
    BOOL result1 = WritePrivateProfileStringA("Window", "X", "-1", fullPath); // CW_USEDEFAULT
    BOOL result2 = WritePrivateProfileStringA("Window", "Y", "-1", fullPath); // CW_USEDEFAULT

    char buffer[32];
    sprintf(buffer, "%d", DEFAULT_WINDOW_WIDTH);
    WritePrivateProfileStringA("Window", "Width", buffer, fullPath);

    sprintf(buffer, "%d", DEFAULT_WINDOW_HEIGHT);
    WritePrivateProfileStringA("Window", "Height", buffer, fullPath);

    WritePrivateProfileStringA("Window", "Maximized", "0", fullPath);

    // Write default splitter position
    WritePrivateProfileStringA("Splitter", "Position", "400", fullPath);

    // Write default column settings
    WritePrivateProfileStringA("Columns", "Width0", "200", fullPath); // 名称
    WritePrivateProfileStringA("Columns", "Width1", "300", fullPath); // 路径
    WritePrivateProfileStringA("Columns", "Width2", "100", fullPath); // 大小
    WritePrivateProfileStringA("Columns", "Width3", "150", fullPath); // 修改时间

    WritePrivateProfileStringA("Columns", "Order0", "0", fullPath); // 名称
    WritePrivateProfileStringA("Columns", "Order1", "1", fullPath); // 路径
    WritePrivateProfileStringA("Columns", "Order2", "2", fullPath); // 大小
    WritePrivateProfileStringA("Columns", "Order3", "3", fullPath); // 修改时间

    // Add comments section for user reference
    WritePrivateProfileStringA("Comments", "Description", "File Previewer Configuration", fullPath);
    WritePrivateProfileStringA("Comments", "WindowX", "Window X position (-1 = default)", fullPath);
    WritePrivateProfileStringA("Comments", "WindowY", "Window Y position (-1 = default)", fullPath);
    WritePrivateProfileStringA("Comments", "SplitterPosition", "Splitter position in pixels", fullPath);
    WritePrivateProfileStringA("Comments", "ColumnWidths", "ListView column widths (Name, Path, Size, Modified)", fullPath);
    WritePrivateProfileStringA("Comments", "ColumnOrder", "ListView column display order (0-3)", fullPath);
    WritePrivateProfileStringA("Comments", "ScanStatus", "USN scan status saved in separate scan_status.ini file", fullPath);

    // Flush to disk
    WritePrivateProfileStringA(NULL, NULL, NULL, fullPath);
}

// Load configuration from INI file
void LoadConfiguration()
{

    // Set defaults
    g_config.windowX = CW_USEDEFAULT;
    g_config.windowY = CW_USEDEFAULT;
    g_config.windowWidth = DEFAULT_WINDOW_WIDTH;
    g_config.windowHeight = DEFAULT_WINDOW_HEIGHT;
    g_config.windowMaximized = FALSE;
    g_config.splitterPosition = 400;

    // Scan status functionality removed

    // Set default column settings
    g_config.columnWidths[0] = 200; // 名称
    g_config.columnWidths[1] = 300; // 路径
    g_config.columnWidths[2] = 100; // 大小
    g_config.columnWidths[3] = 150; // 修改时间

    g_config.columnOrder[0] = 0; // 名称
    g_config.columnOrder[1] = 1; // 路径
    g_config.columnOrder[2] = 2; // 大小
    g_config.columnOrder[3] = 3; // 修改时间

    // 使用绝对路径检查和读取配置文件
    char fullPath[MAX_PATH];
    GetCurrentDirectoryA(MAX_PATH, fullPath);
    strcat(fullPath, "\\");
    strcat(fullPath, CONFIG_FILE);

    // Check if config file exists
    if (GetFileAttributesA(fullPath) == INVALID_FILE_ATTRIBUTES)
    {
        CreateDefaultConfigFile();
        return;
    }

    // Load settings from INI file
    g_config.windowX = GetPrivateProfileIntA("Window", "X", g_config.windowX, fullPath);
    g_config.windowY = GetPrivateProfileIntA("Window", "Y", g_config.windowY, fullPath);
    g_config.windowWidth = GetPrivateProfileIntA("Window", "Width", g_config.windowWidth, fullPath);
    g_config.windowHeight = GetPrivateProfileIntA("Window", "Height", g_config.windowHeight, fullPath);
    g_config.windowMaximized = GetPrivateProfileIntA("Window", "Maximized", g_config.windowMaximized, fullPath);
    g_config.splitterPosition = GetPrivateProfileIntA("Splitter", "Position", g_config.splitterPosition, fullPath);

    // Scan status functionality removed

    // Load column settings
    g_config.columnWidths[0] = GetPrivateProfileIntA("Columns", "Width0", g_config.columnWidths[0], fullPath);
    g_config.columnWidths[1] = GetPrivateProfileIntA("Columns", "Width1", g_config.columnWidths[1], fullPath);
    g_config.columnWidths[2] = GetPrivateProfileIntA("Columns", "Width2", g_config.columnWidths[2], fullPath);
    g_config.columnWidths[3] = GetPrivateProfileIntA("Columns", "Width3", g_config.columnWidths[3], fullPath);

    g_config.columnOrder[0] = GetPrivateProfileIntA("Columns", "Order0", g_config.columnOrder[0], fullPath);
    g_config.columnOrder[1] = GetPrivateProfileIntA("Columns", "Order1", g_config.columnOrder[1], fullPath);
    g_config.columnOrder[2] = GetPrivateProfileIntA("Columns", "Order2", g_config.columnOrder[2], fullPath);
    g_config.columnOrder[3] = GetPrivateProfileIntA("Columns", "Order3", g_config.columnOrder[3], fullPath);
}

// Save configuration to INI file
void SaveConfiguration()
{
    if (!hwndMain)
        return;

    // 使用绝对路径来确保文件保存成功
    char fullPath[MAX_PATH];
    GetCurrentDirectoryA(MAX_PATH, fullPath);
    strcat(fullPath, "\\");
    strcat(fullPath, CONFIG_FILE);

    // Get current window position and size
    RECT rect;
    GetWindowRect(hwndMain, &rect);

    BOOL isMaximized = IsZoomed(hwndMain);

    if (!isMaximized)
    {
        g_config.windowX = rect.left;
        g_config.windowY = rect.top;
        g_config.windowWidth = rect.right - rect.left;
        g_config.windowHeight = rect.bottom - rect.top;
    }
    g_config.windowMaximized = isMaximized;
    g_config.splitterPosition = splitterPos;

    // Save to INI file
    char buffer[32];

    sprintf(buffer, "%d", g_config.windowX);
    WritePrivateProfileStringA("Window", "X", buffer, fullPath);

    sprintf(buffer, "%d", g_config.windowY);
    WritePrivateProfileStringA("Window", "Y", buffer, fullPath);

    sprintf(buffer, "%d", g_config.windowWidth);
    WritePrivateProfileStringA("Window", "Width", buffer, fullPath);

    sprintf(buffer, "%d", g_config.windowHeight);
    WritePrivateProfileStringA("Window", "Height", buffer, fullPath);

    sprintf(buffer, "%d", g_config.windowMaximized);
    WritePrivateProfileStringA("Window", "Maximized", buffer, fullPath);

    sprintf(buffer, "%d", g_config.splitterPosition);
    WritePrivateProfileStringA("Splitter", "Position", buffer, fullPath);

    // 扫描状态现在保存在独立文件中，不在这里保存

    // Save column settings
    sprintf(buffer, "%d", g_config.columnWidths[0]);
    WritePrivateProfileStringA("Columns", "Width0", buffer, fullPath);
    sprintf(buffer, "%d", g_config.columnWidths[1]);
    WritePrivateProfileStringA("Columns", "Width1", buffer, fullPath);
    sprintf(buffer, "%d", g_config.columnWidths[2]);
    WritePrivateProfileStringA("Columns", "Width2", buffer, fullPath);
    sprintf(buffer, "%d", g_config.columnWidths[3]);
    WritePrivateProfileStringA("Columns", "Width3", buffer, fullPath);

    sprintf(buffer, "%d", g_config.columnOrder[0]);
    WritePrivateProfileStringA("Columns", "Order0", buffer, fullPath);
    sprintf(buffer, "%d", g_config.columnOrder[1]);
    WritePrivateProfileStringA("Columns", "Order1", buffer, fullPath);
    sprintf(buffer, "%d", g_config.columnOrder[2]);
    WritePrivateProfileStringA("Columns", "Order2", buffer, fullPath);
    sprintf(buffer, "%d", g_config.columnOrder[3]);
    WritePrivateProfileStringA("Columns", "Order3", buffer, fullPath);

    // Flush to disk
    WritePrivateProfileStringA(NULL, NULL, NULL, fullPath);
}

// Apply configuration to window
void ApplyConfiguration()
{
    if (!hwndMain)
        return;

    // Apply window position and size (only if not using default values)
    if (g_config.windowX > 0 && g_config.windowY > 0 &&
        g_config.windowWidth > 0 && g_config.windowHeight > 0)
    {
        SetWindowPos(hwndMain, NULL,
                     g_config.windowX, g_config.windowY,
                     g_config.windowWidth, g_config.windowHeight,
                     SWP_NOZORDER);
    }

    // Apply maximized state
    if (g_config.windowMaximized)
    {
        ShowWindow(hwndMain, SW_MAXIMIZE);
    }

    // Apply splitter position
    if (g_config.splitterPosition > 0)
    {
        splitterPos = g_config.splitterPosition;
        UpdateLayout(hwndMain);
    }
}

// Save column settings (widths and order)
void SaveColumnSettings()
{
    if (!hwndLeft)
        return;

    // Get current column widths
    for (int i = 0; i < 4; i++)
    {
        int width = ListView_GetColumnWidth(hwndLeft, i);
        if (width > 0)
        {
            // Map display column index to logical column index
            int logicalIndex = g_config.columnOrder[i];
            g_config.columnWidths[logicalIndex] = width;
        }
    }

    // Get current column order
    int order[4];
    if (ListView_GetColumnOrderArray(hwndLeft, 4, order))
    {
        // Update column order in configuration
        for (int i = 0; i < 4; i++)
        {
            g_config.columnOrder[i] = order[i];
        }
    }

    // Save configuration to file
    SaveConfiguration();
}

// Scan status functions removed

// Check database file size stability (30 seconds without change)
BOOL CheckDatabaseSizeStability()
{
    const char *dbPath = "file_index.db";
    const int STABILITY_DURATION = 30; // 30 seconds
    const int CHECK_INTERVAL = 2;      // Check every 2 seconds

    LONGLONG lastSize = -1;
    time_t lastChangeTime = time(NULL);
    int checkCount = 0;

    while (checkCount < (STABILITY_DURATION / CHECK_INTERVAL))
    {
        // Get current database file size
        WIN32_FIND_DATAA findData;
        HANDLE hFind = FindFirstFileA(dbPath, &findData);

        if (hFind == INVALID_HANDLE_VALUE)
        {
            return FALSE;
        }

        LARGE_INTEGER fileSize;
        fileSize.LowPart = findData.nFileSizeLow;
        fileSize.HighPart = findData.nFileSizeHigh;
        FindClose(hFind);

        LONGLONG currentSize = fileSize.QuadPart;
        time_t currentTime = time(NULL);

        if (lastSize == -1)
        {
            // First check
            lastSize = currentSize;
            lastChangeTime = currentTime;
        }
        else if (currentSize != lastSize)
        {
            // Size changed, reset timer
            lastSize = currentSize;
            lastChangeTime = currentTime;
            checkCount = 0; // Reset counter
        }
        else
        {
            // Size unchanged, check if stable duration reached
            int stableDuration = (int)(currentTime - lastChangeTime);

            if (stableDuration >= STABILITY_DURATION)
            {
                return TRUE;
            }
        }

        // Wait before next check
        Sleep(CHECK_INTERVAL * 1000);
        checkCount++;
    }

    return FALSE;
}

// Database monitoring functions removed - using USN memory only

// Database functions removed - using USN memory only

// USN Memory management functions
void InitializeUSNMemory()
{
    InitializeCriticalSection(&g_usnDataCriticalSection);
    g_usnFileCapacity = 4069000; // 直接分配最大容量，避免重新分配
    g_usnFiles = (USNFileData *)malloc(g_usnFileCapacity * sizeof(USNFileData));
    g_usnFileCount = 0;
}

// 清理USN内存
void CleanupUSNMemory()
{
    EnterCriticalSection(&g_usnDataCriticalSection);

    // 释放主数组（不需要释放字符串，因为现在是固定数组）
    if (g_usnFiles)
    {
        free(g_usnFiles);
        g_usnFiles = NULL;
    }

    g_usnFileCount = 0;
    g_usnFileCapacity = 0;

    LeaveCriticalSection(&g_usnDataCriticalSection);
    DeleteCriticalSection(&g_usnDataCriticalSection);
}

// 初始化筛选线程
void InitializeFilterThread()
{
    InitializeCriticalSection(&g_filterCriticalSection);
    g_filterThreadRunning = TRUE;
    g_filterPending = FALSE;

    g_filterThread = CreateThread(NULL, 0, FilterThreadProc, NULL, 0, &g_filterThreadId);
    if (g_filterThread == NULL)
    {
        g_filterThreadRunning = FALSE;
    }
}

// 清理筛选线程
void CleanupFilterThread()
{
    if (g_filterThreadRunning)
    {
        g_filterThreadRunning = FALSE;

        if (g_filterThread)
        {
            WaitForSingleObject(g_filterThread, 5000); // 等待5秒
            CloseHandle(g_filterThread);
            g_filterThread = NULL;
        }
    }

    DeleteCriticalSection(&g_filterCriticalSection);
}

// 请求筛选操作
void RequestFilter(const WCHAR *filterText)
{
    // 检查筛选线程和数据状态
    if (!g_filterThreadRunning || !g_dataLoaded || !g_usnFiles || g_usnFileCount == 0)
    {
        return;
    }

    EnterCriticalSection(&g_filterCriticalSection);

    // 更新待处理的筛选文本
    if (filterText)
    {
        wcsncpy(g_pendingFilterText, filterText, 255);
        g_pendingFilterText[255] = L'\0';
    }
    else
    {
        g_pendingFilterText[0] = L'\0';
    }

    g_filterPending = TRUE;

    LeaveCriticalSection(&g_filterCriticalSection);
}

// 位图筛选辅助函数实现
void InitializeFilterBitmap()
{
    if (g_usnFileCapacity > 0)
    {
        g_bitmapSize = (g_usnFileCapacity + 7) / 8; // 每8个文件需要1字节
        g_filterBitmap = (BYTE *)calloc(g_bitmapSize, 1);
    }
}

void CleanupFilterBitmap()
{
    if (g_filterBitmap)
    {
        free(g_filterBitmap);
        g_filterBitmap = NULL;
        g_bitmapSize = 0;
    }
}

void SetFilterBit(int index, BOOL value)
{
    if (!g_filterBitmap || index < 0 || index >= g_usnFileCapacity)
        return;

    int byteIndex = index / 8;
    int bitIndex = index % 8;

    if (value)
        g_filterBitmap[byteIndex] |= (1 << bitIndex);
    else
        g_filterBitmap[byteIndex] &= ~(1 << bitIndex);
}

BOOL GetFilterBit(int index)
{
    if (!g_filterBitmap || index < 0 || index >= g_usnFileCapacity)
        return FALSE;

    int byteIndex = index / 8;
    int bitIndex = index % 8;

    return (g_filterBitmap[byteIndex] & (1 << bitIndex)) != 0;
}

int GetFilteredIndexByPosition(int position)
{
    if (!g_filterBitmap || position < 0)
        return -1;

    int count = 0;
    for (int i = 0; i < g_usnFileCount; i++)
    {
        if (GetFilterBit(i))
        {
            if (count == position)
                return i;
            count++;
        }
    }

    return -1;
}

// ==================== 右键菜单功能实现 ====================

// 创建右键菜单
HMENU CreateFileContextMenu()
{
    HMENU hMenu = CreatePopupMenu();
    AppendMenu(hMenu, MF_STRING, ID_OPEN_FOLDER, L"打开文件夹");
    AppendMenu(hMenu, MF_STRING, ID_OPEN_FILE, L"打开文件");
    AppendMenu(hMenu, MF_SEPARATOR, 0, NULL);
    AppendMenu(hMenu, MF_STRING, ID_COPY_PATH, L"复制路径");
    AppendMenu(hMenu, MF_STRING, ID_PROPERTIES, L"属性");
    return hMenu;
}

// 获取实际文件索引（考虑筛选）
int GetActualFileIndex(int selectedIndex)
{
    if (g_filterActive && g_filterBitmap)
    {
        return GetFilteredIndexByPosition(selectedIndex);
    }
    return selectedIndex;
}

// 构建完整文件路径
void BuildFullFilePath(int actualIndex, WCHAR *fullPath)
{
    if (actualIndex >= 0 && actualIndex < g_usnFileCount)
    {
        swprintf(fullPath, MAX_PATH * 2, L"%s\\%s",
                 g_usnFiles[actualIndex].fullPath,
                 g_usnFiles[actualIndex].fileName);
    }
    else
    {
        fullPath[0] = L'\0';
    }
}

// 打开文件夹功能
void OpenFileFolder(const WCHAR *filePath)
{
    if (!filePath || wcslen(filePath) == 0)
    {
        return;
    }

    // 使用explorer.exe /select参数打开文件夹并选中文件
    WCHAR command[MAX_PATH * 3];
    swprintf(command, MAX_PATH * 3, L"explorer.exe /select,\"%s\"", filePath);

    STARTUPINFO si = {0};
    PROCESS_INFORMATION pi = {0};
    si.cb = sizeof(si);

    if (CreateProcess(NULL, command, NULL, NULL, FALSE, 0, NULL, NULL, &si, &pi))
    {
        CloseHandle(pi.hProcess);
        CloseHandle(pi.hThread);
    }
}

// 打开文件功能
void OpenFileWithDefaultApp(const WCHAR *filePath)
{
    if (!filePath || wcslen(filePath) == 0)
    {
        return;
    }

    // 使用默认程序打开文件
    HINSTANCE result = ShellExecute(NULL, L"open", filePath, NULL, NULL, SW_SHOWNORMAL);

    if ((INT_PTR)result <= 32)
    {
        // 打开失败
    }
}

// 复制路径到剪贴板
void CopyPathToClipboard(const WCHAR *filePath)
{
    if (!filePath || wcslen(filePath) == 0)
    {
        return;
    }

    if (OpenClipboard(hwndMain))
    {
        EmptyClipboard();

        int len = (wcslen(filePath) + 1) * sizeof(WCHAR);
        HGLOBAL hMem = GlobalAlloc(GMEM_MOVEABLE, len);

        if (hMem)
        {
            WCHAR *pMem = (WCHAR *)GlobalLock(hMem);
            wcscpy(pMem, filePath);
            GlobalUnlock(hMem);

            SetClipboardData(CF_UNICODETEXT, hMem);
        }

        CloseClipboard();
    }
}

// 显示文件属性
void ShowFileProperties(const WCHAR *filePath)
{
    if (!filePath || wcslen(filePath) == 0)
    {
        return;
    }

    SHELLEXECUTEINFO sei = {0};
    sei.cbSize = sizeof(sei);
    sei.fMask = SEE_MASK_INVOKEIDLIST;
    sei.lpVerb = L"properties";
    sei.lpFile = filePath;
    sei.nShow = SW_SHOWNORMAL;

    ShellExecuteEx(&sei);
}

// 处理菜单命令
void HandleMenuCommand(int cmd, const WCHAR *filePath)
{
    switch (cmd)
    {
    case ID_OPEN_FOLDER:
        OpenFileFolder(filePath);
        break;

    case ID_OPEN_FILE:
        OpenFileWithDefaultApp(filePath);
        break;

    case ID_COPY_PATH:
        CopyPathToClipboard(filePath);
        break;

    case ID_PROPERTIES:
        ShowFileProperties(filePath);
        break;

    default:
        break;
    }
}

// 显示右键菜单
void ShowContextMenu(POINT pt, const WCHAR *filePath)
{
    // 转换为屏幕坐标
    ClientToScreen(hwndLeft, &pt);

    // 创建菜单
    HMENU hMenu = CreateFileContextMenu();

    // 显示菜单并获取用户选择
    int cmd = TrackPopupMenu(hMenu,
                             TPM_RETURNCMD | TPM_RIGHTBUTTON,
                             pt.x, pt.y, 0, hwndMain, NULL);

    // 处理菜单选择
    if (cmd > 0)
    {
        HandleMenuCommand(cmd, filePath);
    }

    // 清理菜单
    DestroyMenu(hMenu);
}

// 处理ListView右键点击
void HandleListViewRightClick(LPNMITEMACTIVATE pnmia)
{
    // 获取点击位置的文件索引
    int selectedIndex = pnmia->iItem;
    if (selectedIndex == -1)
    {
        return;
    }

    // 获取实际文件索引（考虑筛选）
    int actualIndex = GetActualFileIndex(selectedIndex);
    if (actualIndex == -1)
    {
        return;
    }

    // 构建完整文件路径
    WCHAR fullPath[MAX_PATH * 2];
    BuildFullFilePath(actualIndex, fullPath);

    if (wcslen(fullPath) == 0)
    {
        return;
    }

    // 显示右键菜单
    ShowContextMenu(pnmia->ptAction, fullPath);
}

// 筛选线程主函数
DWORD WINAPI FilterThreadProc(LPVOID lpParam)
{

    while (g_filterThreadRunning)
    {
        BOOL needFilter = FALSE;
        WCHAR localFilterText[256] = L"";

        // 检查是否有待处理的筛选请求
        EnterCriticalSection(&g_filterCriticalSection);
        if (g_filterPending)
        {
            wcscpy(localFilterText, g_pendingFilterText);
            g_filterPending = FALSE;
            needFilter = TRUE;
        }
        LeaveCriticalSection(&g_filterCriticalSection);

        if (needFilter)
        {
            // 执行筛选操作
            PerformFilterOperation(localFilterText);
        }

        // 动态调整休眠时间，提高响应速度
        Sleep(g_filteredCount > 100000 ? 30 : 10);
    }

    return 0;
}

// 执行实际的筛选操作
void PerformFilterOperation(const WCHAR *filterText)
{
    // 多重安全检查
    if (!g_usnFiles || g_usnFileCount == 0 || !g_dataLoaded)
    {
        return;
    }

    // 筛选缓存检查 - 避免重复筛选
    if (g_filterCacheValid && wcscmp(g_lastFilterText, filterText ? filterText : L"") == 0)
    {
        // 通知UI更新
        if (hwndMain && IsWindow(hwndMain))
        {
            PostMessage(hwndMain, WM_USER + 6, 0, 0);
        }
        return;
    }

    DWORD filterStartTime = GetTickCount();

    EnterCriticalSection(&g_dataCriticalSection);

    // Update current filter
    if (filterText && wcslen(filterText) > 0)
    {
        wcsncpy(g_currentFilter, filterText, MAX_FILTER_LENGTH - 1);
        g_currentFilter[MAX_FILTER_LENGTH - 1] = L'\0';
        g_filterActive = TRUE;
    }
    else
    {
        g_currentFilter[0] = L'\0';
        g_filterActive = FALSE;
    }

    // 如果没有筛选，显示所有文件
    if (!g_filterActive)
    {
        g_filteredCount = g_usnFileCount;
        g_virtualItemCount = g_usnFileCount;

        // 清除筛选位图
        if (g_filterBitmap)
        {
            memset(g_filterBitmap, 0xFF, g_bitmapSize); // 设置所有位为1（显示所有文件）
        }
    }
    else
    {
        // 确保位图已初始化
        if (!g_filterBitmap)
        {
            InitializeFilterBitmap();
        }

        if (!g_filterBitmap)
        {
            LeaveCriticalSection(&g_dataCriticalSection);
            return;
        }

        // 清除位图
        memset(g_filterBitmap, 0, g_bitmapSize);

        // 优化的批量筛选操作
        g_filteredCount = 0;
        PerformBatchFiltering(g_currentFilter);

        g_virtualItemCount = g_filteredCount;
    }

    // 更新筛选缓存
    wcscpy(g_lastFilterText, filterText ? filterText : L"");
    g_filterCacheValid = TRUE;

    // 更新性能统计
    DWORD filterTime = GetTickCount() - filterStartTime;
    g_totalFilterTime += filterTime;
    g_filterOperationCount++;

    double avgFilterTime = (double)g_totalFilterTime / g_filterOperationCount;

    LeaveCriticalSection(&g_dataCriticalSection);

    // 通知主线程更新UI
    if (hwndMain && IsWindow(hwndMain))
    {
        PostMessage(hwndMain, WM_USER + 6, 0, 0); // 自定义消息：筛选完成
    }
}

void AddUSNFile(const WCHAR *fileName, const WCHAR *fullPath, LONGLONG fileSize, FILETIME lastWriteTime, DWORD fileAttributes)
{
    // 快速检查容量限制，避免临界区
    if (g_usnFileCount >= g_usnFileCapacity)
    {
        return; // 已达到预分配容量限制
    }

    EnterCriticalSection(&g_usnDataCriticalSection);

    // 再次检查（双重检查锁定模式）
    if (g_usnFileCount >= g_usnFileCapacity)
    {
        LeaveCriticalSection(&g_usnDataCriticalSection);
        return;
    }

    // Add file data
    USNFileData *file = &g_usnFiles[g_usnFileCount];

    // 复制文件名（限制长度为39个字符）
    wcsncpy(file->fileName, fileName, 31);
    file->fileName[31] = L'\0';

    // 直接在目标位置提取目录路径，避免额外复制
    wcsncpy(file->fullPath, fullPath, 63);
    file->fullPath[63] = L'\0';

    // 找到最后一个反斜杠，截取目录部分
    WCHAR *lastSlash = wcsrchr(file->fullPath, L'\\');
    if (lastSlash != NULL)
    {
        *lastSlash = L'\0'; // 截断，只保留目录路径
    }

    // 压缩文件大小（限制为4GB）
    file->fileSize = (DWORD)(fileSize > 0xFFFFFFFF ? 0xFFFFFFFF : fileSize);

    // 转换FILETIME为Unix时间戳（32位）
    ULARGE_INTEGER ull;
    ull.LowPart = lastWriteTime.dwLowDateTime;
    ull.HighPart = lastWriteTime.dwHighDateTime;

    // 转换为Unix时间戳（秒）
    ULONGLONG unixTime = (ull.QuadPart - 116444736000000000ULL) / 10000000ULL;
    file->lastWriteTime = (DWORD)(unixTime > 0xFFFFFFFF ? 0xFFFFFFFF : unixTime);

    // 设置是否为文件夹
    file->isDirectory = (fileAttributes & FILE_ATTRIBUTE_DIRECTORY) ? 1 : 0;

    // 移除预计算字符串，改为动态生成以节省内存

    g_usnFileCount++;

    LeaveCriticalSection(&g_usnDataCriticalSection);

    // 在1000个文件时进行一次早期渲染，让用户尽快看到内容
    if (g_usnFileCount == 1000)
    {
        if (hwndMain && IsWindow(hwndMain))
        {
            PostMessage(hwndMain, WM_USER + 5, 0, 0);
        }
    }

    // 更频繁地更新进度显示，但不更新文件列表
    if (g_usnFileCount % 50000 == 0)
    {
        if (hwndStatus && IsWindow(hwndStatus))
        {
            WCHAR progressText[256];
            swprintf(progressText, 256, L"正在扫描文件... 已找到 %d 个文件", g_usnFileCount);
            SendMessageW(hwndStatus, SB_SETTEXTW, 0, (LPARAM)progressText);
        }
    }

    // 减少文件列表更新频率
    if (g_usnFileCount % 500000 == 0)
    {
        if (hwndMain && IsWindow(hwndMain))
        {
            PostMessage(hwndMain, WM_USER + 5, 0, 0);
        }
    }
}

// USN读取线程 - 简化版本，只读取文件名、路径、大小、修改时间
DWORD WINAPI USNReadThreadProc(LPVOID lpParam)
{
    g_usnReadInProgress = TRUE;

    // 在状态栏显示开始扫描信息
    if (hwndStatus && IsWindow(hwndStatus))
    {
        SendMessageW(hwndStatus, SB_SETTEXTW, 0, (LPARAM)L"开始扫描文件系统...");
    }

    // Update status bar
    if (hwndStatus)
    {
        SendMessageW(hwndStatus, SB_SETTEXTW, 0, (LPARAM)L"正在读取USN数据...");
    }

    // 调用优化的多线程USN读取函数
    int result = ReadUSNToMemoryMultiThreaded();

    if (result == 0)
    {

        // Update status bar with completion info
        if (hwndStatus)
        {
            WCHAR statusText[256];
            swprintf(statusText, 256, L"扫描完成 - 共找到 %d 个文件，正在加载到界面...", g_usnFileCount);
            SendMessageW(hwndStatus, SB_SETTEXTW, 0, (LPARAM)statusText);
        }

        // 触发文件树渲染
        if (hwndMain && IsWindow(hwndMain))
        {
            PostMessage(hwndMain, WM_USER + 5, 0, 0); // 触发渲染
        }
    }
    else
    {

        // Update status with error
        if (hwndStatus)
        {
            SendMessageW(hwndStatus, SB_SETTEXTW, 0, (LPARAM)L"USN读取失败");
        }
    }

    g_usnReadInProgress = FALSE;
    return result;
}

// 启动USN读取线程
void StartUSNReadThread()
{
    if (g_usnReadInProgress)
        return;

    g_usnReadThread = CreateThread(NULL, 0, USNReadThreadProc, NULL, 0, NULL);
}

// USN Journal scan functions removed

// Delayed database loading thread - waits for window completion then loads data
DWORD WINAPI DelayedDatabaseLoadingThreadProc(LPVOID lpParam)
{
    // 等待主窗口完全准备就绪
    int waitCount = 0;
    while (!g_mainWindowReady && waitCount < 100) // 最多等待20秒
    {
        Sleep(200);
        waitCount++;
    }

    if (!g_mainWindowReady)
    {
        return 1;
    }

    // 额外等待2秒确保窗口完全渲染完成
    Sleep(2000);

    // 实时渲染模式：监控USN内存数据变化并更新文件树
    int lastFileCount = 0;
    int renderCount = 0;

    // USN扫描功能已删除，延迟数据库加载线程不再需要

    return 0;
}

// Start delayed database loading
void StartDelayedDatabaseLoading()
{
    HANDLE hThread = CreateThread(NULL, 0, DelayedDatabaseLoadingThreadProc, NULL, 0, NULL);
    if (hThread)
    {
        CloseHandle(hThread);
    }
}

// USN scan functions removed

// USN scan functions removed

// File monitoring thread
DWORD WINAPI FileWatchThreadProc(LPVOID lpParam)
{
    g_fileWatchActive = TRUE;

    HANDLE hDir = CreateFileW(
        L"C:\\",
        FILE_LIST_DIRECTORY,
        FILE_SHARE_READ | FILE_SHARE_WRITE | FILE_SHARE_DELETE,
        NULL,
        OPEN_EXISTING,
        FILE_FLAG_BACKUP_SEMANTICS,
        NULL);

    if (hDir == INVALID_HANDLE_VALUE)
    {
        g_fileWatchActive = FALSE;
        return 1;
    }

    BYTE buffer[4096];
    DWORD bytesReturned;

    while (g_fileWatchActive)
    {
        if (ReadDirectoryChangesW(
                hDir,
                buffer,
                sizeof(buffer),
                TRUE, // Watch subdirectories
                FILE_NOTIFY_CHANGE_FILE_NAME | FILE_NOTIFY_CHANGE_SIZE | FILE_NOTIFY_CHANGE_LAST_WRITE,
                &bytesReturned,
                NULL,
                NULL))
        {
            FILE_NOTIFY_INFORMATION *pNotify = (FILE_NOTIFY_INFORMATION *)buffer;

            do
            {
                WCHAR fileName[MAX_PATH];
                wcsncpy(fileName, pNotify->FileName, pNotify->FileNameLength / sizeof(WCHAR));
                fileName[pNotify->FileNameLength / sizeof(WCHAR)] = L'\0';

                switch (pNotify->Action)
                {
                case FILE_ACTION_ADDED:
                    break;
                case FILE_ACTION_REMOVED:
                    break;
                case FILE_ACTION_MODIFIED:
                    break;
                }

                // Move to next notification
                if (pNotify->NextEntryOffset == 0)
                    break;
                pNotify = (FILE_NOTIFY_INFORMATION *)((BYTE *)pNotify + pNotify->NextEntryOffset);

            } while (TRUE);

            // Refresh UI periodically
            PostMessage(hwndMain, WM_USER + 2, 0, 0);
        }
        else
        {
            DWORD error = GetLastError();
            if (error != ERROR_OPERATION_ABORTED)
            {
                // 监控错误
            }
            break;
        }
    }

    CloseHandle(hDir);
    g_fileWatchActive = FALSE;
    return 0;
}

// Start file watcher
void StartFileWatcher()
{
    if (g_fileWatchActive)
        return;

    g_fileWatchThread = CreateThread(NULL, 0, FileWatchThreadProc, NULL, 0, NULL);
}

// Stop file watcher
void StopFileWatcher()
{
    if (!g_fileWatchActive)
        return;

    g_fileWatchActive = FALSE;

    if (g_fileWatchThread)
    {
        WaitForSingleObject(g_fileWatchThread, 3000);
        CloseHandle(g_fileWatchThread);
        g_fileWatchThread = NULL;
    }
}

// Check if file is a text file
BOOL IsTextFile(const WCHAR *filePath)
{
    if (!filePath)
        return FALSE;

    const WCHAR *ext = wcsrchr(filePath, L'.');
    if (!ext)
        return FALSE;

    const WCHAR *textExts[] = {
        L".txt", L".log", L".ini", L".cfg", L".conf", L".xml", L".json",
        L".html", L".htm", L".css", L".js", L".c", L".cpp", L".h", L".hpp",
        L".py", L".java", L".cs", L".php", L".sql", L".md", L".readme"};

    for (int i = 0; i < sizeof(textExts) / sizeof(textExts[0]); i++)
    {
        if (_wcsicmp(ext, textExts[i]) == 0)
            return TRUE;
    }

    return FALSE;
}

// Check if file is an image file
BOOL IsImageFile(const WCHAR *filePath)
{
    if (!filePath)
        return FALSE;

    const WCHAR *ext = wcsrchr(filePath, L'.');
    if (!ext)
        return FALSE;

    const WCHAR *imageExts[] = {
        L".jpg", L".jpeg", L".png", L".gif", L".bmp", L".ico", L".tiff", L".tif"};

    for (int i = 0; i < sizeof(imageExts) / sizeof(imageExts[0]); i++)
    {
        if (_wcsicmp(ext, imageExts[i]) == 0)
            return TRUE;
    }

    return FALSE;
}

// Load file preview content
void LoadFilePreview(const WCHAR *filePath)
{
    if (!hwndRight || !filePath)
        return;

    // Check if file exists
    if (GetFileAttributesW(filePath) == INVALID_FILE_ATTRIBUTES)
    {
        SetControlTextUTF8(hwndRight, L"文件不存在或无法访问。");
        return;
    }

    // Get file info
    WIN32_FIND_DATAW findData;
    HANDLE hFind = FindFirstFileW(filePath, &findData);
    if (hFind == INVALID_HANDLE_VALUE)
    {
        SetControlTextUTF8(hwndRight, L"无法获取文件信息。");
        return;
    }
    FindClose(hFind);

    // Format file size
    WCHAR sizeStr[64];
    LARGE_INTEGER fileSize;
    fileSize.LowPart = findData.nFileSizeLow;
    fileSize.HighPart = findData.nFileSizeHigh;

    if (fileSize.QuadPart < 1024)
        swprintf(sizeStr, 64, L"%lld bytes", fileSize.QuadPart);
    else if (fileSize.QuadPart < 1024 * 1024)
        swprintf(sizeStr, 64, L"%.1f KB", fileSize.QuadPart / 1024.0);
    else if (fileSize.QuadPart < 1024 * 1024 * 1024)
        swprintf(sizeStr, 64, L"%.1f MB", fileSize.QuadPart / (1024.0 * 1024.0));
    else
        swprintf(sizeStr, 64, L"%.1f GB", fileSize.QuadPart / (1024.0 * 1024.0 * 1024.0));

    // Format modification time (格式: 2024-07-25 07:18)
    WCHAR timeStr[64];
    SYSTEMTIME st;
    FileTimeToSystemTime(&findData.ftLastWriteTime, &st);
    swprintf(timeStr, 64, L"%04d-%02d-%02d %02d:%02d",
             st.wYear, st.wMonth, st.wDay, st.wHour, st.wMinute);

    // Build preview content
    WCHAR previewContent[8192];
    swprintf(previewContent, 8192,
             L"文件预览\r\n"
             L"========================================\r\n"
             L"文件名：%s\r\n"
             L"路径：%s\r\n"
             L"大小：%s\r\n"
             L"修改时间：%s\r\n"
             L"========================================\r\n\r\n",
             wcsrchr(filePath, L'\\') ? wcsrchr(filePath, L'\\') + 1 : filePath,
             filePath,
             sizeStr,
             timeStr);

    // Try to load content based on file type
    if (IsTextFile(filePath) && fileSize.QuadPart < 1024 * 1024) // Max 1MB for text preview
    {
        wcscat(previewContent, L"文件内容：\r\n");
        wcscat(previewContent, L"----------------------------------------\r\n");

        HANDLE hFile = CreateFileW(filePath, GENERIC_READ, FILE_SHARE_READ, NULL, OPEN_EXISTING, 0, NULL);
        if (hFile != INVALID_HANDLE_VALUE)
        {
            DWORD fileSize = GetFileSize(hFile, NULL);
            if (fileSize > 0 && fileSize < 4096) // Limit preview size
            {
                char *buffer = (char *)malloc(fileSize + 1);
                if (buffer)
                {
                    DWORD bytesRead;
                    if (ReadFile(hFile, buffer, fileSize, &bytesRead, NULL))
                    {
                        buffer[bytesRead] = '\0';

                        // Convert to wide characters
                        WCHAR *wideBuffer = (WCHAR *)malloc((bytesRead + 1) * sizeof(WCHAR));
                        if (wideBuffer)
                        {
                            MultiByteToWideChar(CP_UTF8, 0, buffer, -1, wideBuffer, bytesRead + 1);
                            wcscat(previewContent, wideBuffer);
                            free(wideBuffer);
                        }
                    }
                    free(buffer);
                }
            }
            else
            {
                wcscat(previewContent, L"[文件过大，无法预览内容]");
            }
            CloseHandle(hFile);
        }
        else
        {
            wcscat(previewContent, L"[无法读取文件内容]");
        }
    }
    else if (IsImageFile(filePath))
    {
        wcscat(previewContent, L"这是图片文件。\r\n");
        wcscat(previewContent, L"支持格式：JPG、PNG、GIF、BMP 等。\r\n");
        wcscat(previewContent, L"[图片预览功能待实现]");
    }
    else
    {
        wcscat(previewContent, L"这是二进制文件或不支持预览的文件类型。\r\n");
        wcscat(previewContent, L"仅显示基本文件信息。");
    }

    // Set preview content
    SetControlTextUTF8(hwndRight, previewContent);
}

// Preview selected file
void PreviewSelectedFile()
{
    if (!hwndLeft || !g_usnFiles)
        return;

    int selectedIndex = ListView_GetNextItem(hwndLeft, -1, LVNI_SELECTED);
    if (selectedIndex == -1)
    {
        SetControlTextUTF8(hwndRight, L"请选择一个文件进行预览。");
        return;
    }

    // Get actual index (considering filtering)
    int actualIndex = selectedIndex;
    if (g_filterActive && g_filterBitmap)
    {
        actualIndex = GetFilteredIndexByPosition(selectedIndex);
        if (actualIndex == -1)
        {
            SetControlTextUTF8(hwndRight, L"筛选索引错误。");
            return;
        }
    }

    if (actualIndex >= 0 && actualIndex < g_usnFileCount)
    {
        // 重新构建完整的文件路径
        WCHAR fullFilePath[MAX_PATH * 2];
        if (g_usnFiles[actualIndex].fullPath[0] != L'\0' && g_usnFiles[actualIndex].fileName[0] != L'\0')
        {
            // 组合目录路径和文件名
            swprintf(fullFilePath, MAX_PATH * 2, L"%s\\%s",
                     g_usnFiles[actualIndex].fullPath,
                     g_usnFiles[actualIndex].fileName);
        }
        else
        {
            // 如果路径信息不完整，直接使用文件名
            wcscpy(fullFilePath, g_usnFiles[actualIndex].fileName);
        }

        LoadFilePreview(fullFilePath);
    }
}

// Create flat design font with DPI scaling
void CreateFlatFont()
{
    // 使用DPI缩放的字体创建
    hFont = CreateScaledFont(FONT_SIZE, L"Microsoft YaHei Light");

    if (!hFont)
    {
        // 如果微软雅黑Light创建失败，尝试使用普通版
        hFont = CreateScaledFont(FONT_SIZE, L"Microsoft YaHei");
    }

    if (!hFont)
    {
        // 最后的备选方案：使用系统默认字体
        hFont = (HFONT)GetStockObject(DEFAULT_GUI_FONT);
    }

    // Create brushes for flat design
    hBackgroundBrush = CreateSolidBrush(FLAT_COLOR_BACKGROUND);
    hSplitterBrush = CreateSolidBrush(FLAT_COLOR_SPLITTER);
}

// Draw flat splitter with DPI scaling
void DrawSplitter(HDC hdc, RECT *rect)
{
    RECT splitterRect;
    splitterRect.left = splitterPos;
    splitterRect.top = ScaleDPIY(INPUT_HEIGHT);
    splitterRect.right = splitterPos + ScaleDPIX(SPLITTER_WIDTH);
    splitterRect.bottom = rect->bottom - ScaleDPIY(STATUS_HEIGHT);

    FillRect(hdc, &splitterRect, hSplitterBrush);
}

// Create flat style controls with all features
void CreateControls(HWND hwnd)
{
    // Create flat design font first
    CreateFlatFont();

    // 数据库功能已移除，直接使用USN内存

    // Configuration already loaded in WinMain before window creation

    // 全盘文件扫描功能已删除

    // Create status bar
    hwndStatus = CreateWindow(
        STATUSCLASSNAME,
        NULL,
        WS_CHILD | WS_VISIBLE,
        0, 0, 0, 0,
        hwnd,
        NULL,
        GetModuleHandle(NULL),
        NULL);

    if (hwndStatus)
    {
        SendMessage(hwndStatus, WM_SETFONT, (WPARAM)hFont, TRUE);
        // 设置初始状态信息
        SendMessageW(hwndStatus, SB_SETTEXTW, 0, (LPARAM)L"程序启动中，准备扫描文件系统...");
    }

    // Create filter input with DPI scaling
    hwndEdit = CreateWindowW(
        L"EDIT",
        L"",
        WS_CHILD | WS_VISIBLE | ES_LEFT | ES_AUTOHSCROLL,
        ScaleDPIX(BORDER_WIDTH), ScaleDPIY(BORDER_WIDTH),
        ScaleDPIX(400), ScaleDPIY(INPUT_HEIGHT),
        hwnd,
        (HMENU)1001,
        GetModuleHandle(NULL),
        NULL);

    if (hwndEdit)
    {
        SendMessage(hwndEdit, WM_SETFONT, (WPARAM)hFont, TRUE);
        SendMessageW(hwndEdit, EM_SETCUEBANNER, TRUE, (LPARAM)L"输入关键词筛选文件...");
    }

    // Create left ListView with virtual mode support
    hwndLeft = CreateWindowW(
        WC_LISTVIEWW,
        L"",
        WS_CHILD | WS_VISIBLE | LVS_REPORT | LVS_SINGLESEL | LVS_OWNERDATA,
        BORDER_WIDTH, INPUT_HEIGHT,
        splitterPos, 400,
        hwnd,
        (HMENU)1002,
        GetModuleHandle(NULL),
        NULL);

    if (hwndLeft)
    {
        // 设置虚拟ListView扩展样式
        ListView_SetExtendedListViewStyle(hwndLeft,
                                          LVS_EX_FULLROWSELECT | LVS_EX_GRIDLINES | LVS_EX_DOUBLEBUFFER |
                                              LVS_EX_HEADERDRAGDROP);

        SendMessage(hwndLeft, WM_SETFONT, (WPARAM)hFont, TRUE);

        // Add columns using configuration settings
        LVCOLUMNW lvc = {0};
        lvc.mask = LVCF_TEXT | LVCF_WIDTH | LVCF_FMT;

        // Column names
        const WCHAR *columnNames[4] = {L"名称", L"路径", L"大小", L"修改时间"};

        // Create columns in default order first
        for (int i = 0; i < 4; i++)
        {
            lvc.pszText = (WCHAR *)columnNames[i];
            lvc.cx = g_config.columnWidths[i];

            // 设置列对齐方式：大小列（索引2）右对齐，其他列左对齐
            if (i == 2) // 大小列
            {
                lvc.fmt = LVCFMT_RIGHT;
            }
            else
            {
                lvc.fmt = LVCFMT_LEFT;
            }

            SendMessageW(hwndLeft, LVM_INSERTCOLUMNW, i, (LPARAM)&lvc);
        }

        // Apply saved column order
        ListView_SetColumnOrderArray(hwndLeft, 4, g_config.columnOrder);

        // 初始筛选将在数据加载完成后自动应用
    }

    // Create right panel for file preview
    hwndRight = CreateWindowW(
        L"EDIT",
        L"文件预览区\r\n\r\n完整功能：\r\n• 26号微软雅黑字体，30像素行高\r\n• 1像素分割线，0像素边距\r\n• SQLite数据库存储\r\n• USN全盘扫描\r\n• 实时文件监控\r\n• 配置系统\r\n• 文件预览功能\r\n• 实时筛选\r\n• 中文字符正确显示\r\n\r\n请在左侧选择文件以预览。",
        WS_CHILD | WS_VISIBLE | ES_MULTILINE | ES_READONLY | WS_VSCROLL | ES_AUTOVSCROLL,
        splitterPos + SPLITTER_WIDTH, INPUT_HEIGHT,
        400, 400,
        hwnd,
        (HMENU)1003,
        GetModuleHandle(NULL),
        NULL);

    if (hwndRight)
    {
        SendMessage(hwndRight, WM_SETFONT, (WPARAM)hFont, TRUE);
    }
}

// Update layout with flat design and DPI scaling (no gaps)
void UpdateLayout(HWND hwnd)
{
    RECT rect;
    GetClientRect(hwnd, &rect);

    // 使用DPI缩放的尺寸
    int scaledBorderWidth = ScaleDPIX(BORDER_WIDTH);
    int scaledInputHeight = ScaleDPIY(INPUT_HEIGHT);
    int scaledStatusHeight = ScaleDPIY(STATUS_HEIGHT);
    int scaledSplitterWidth = ScaleDPIX(SPLITTER_WIDTH);

    // Status bar at bottom
    if (hwndStatus)
    {
        SetWindowPos(hwndStatus, NULL,
                     0, rect.bottom - scaledStatusHeight,
                     rect.right, scaledStatusHeight,
                     SWP_NOZORDER);
    }

    // Filter input at top (no gap)
    if (hwndEdit)
    {
        SetWindowPos(hwndEdit, NULL,
                     scaledBorderWidth, scaledBorderWidth,
                     rect.right - (2 * scaledBorderWidth), scaledInputHeight,
                     SWP_NOZORDER);
    }

    // Left ListView (no gap)
    if (hwndLeft)
    {
        SetWindowPos(hwndLeft, NULL,
                     scaledBorderWidth, scaledInputHeight,
                     splitterPos - scaledBorderWidth,
                     rect.bottom - scaledInputHeight - scaledStatusHeight,
                     SWP_NOZORDER);
    }

    // Right panel (no gap)
    if (hwndRight)
    {
        SetWindowPos(hwndRight, NULL,
                     splitterPos + scaledSplitterWidth, scaledInputHeight,
                     rect.right - splitterPos - scaledSplitterWidth - scaledBorderWidth,
                     rect.bottom - scaledInputHeight - scaledStatusHeight,
                     SWP_NOZORDER);
    }
}

// Window procedure with all features
LRESULT CALLBACK WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam)
{
    // 过滤频繁的无关消息，只记录重要消息
    if (uMsg != WM_SETCURSOR && uMsg != WM_NCMOUSEMOVE && uMsg != WM_NCHITTEST &&
        uMsg != WM_MOUSEMOVE && uMsg != WM_GETTEXT && uMsg != WM_GETTEXTLENGTH &&
        uMsg != WM_ERASEBKGND && uMsg != WM_NCPAINT && uMsg != 0x00a0 && uMsg != 0x0020 &&
        uMsg != 0x0084 && uMsg != 0x0200 && uMsg != 0x000d && uMsg != 0x000e)
    {
    }

    switch (uMsg)
    {
    case WM_CREATE:
        CreateControls(hwnd);

        // Apply splitter position from configuration
        if (g_config.splitterPosition > 0)
        {
            splitterPos = g_config.splitterPosition;
        }

        UpdateLayout(hwnd);

        // Apply maximized state if needed (position and size already set during window creation)
        if (g_config.windowMaximized)
        {
            ShowWindow(hwnd, SW_MAXIMIZE);
        }

        // 标记程序初始化完成
        g_programInitialized = TRUE;
        g_mainWindowReady = TRUE;

        // 不在这里启动延迟数据库加载，USN扫描线程会处理实时渲染

        return 0;

    case WM_SIZE:
        UpdateLayout(hwnd);
        InvalidateRect(hwnd, NULL, TRUE); // Redraw splitter

        // Save configuration on resize (with delay to avoid frequent saves)
        SetTimer(hwnd, 1, 1000, NULL); // Save after 1 second
        return 0;

    case WM_TIMER:
        if (wParam == 1)
        {
            KillTimer(hwnd, 1);
            SaveConfiguration();
        }
        return 0;

    case WM_PAINT:
    {
        PAINTSTRUCT ps;
        HDC hdc = BeginPaint(hwnd, &ps);

        RECT rect;
        GetClientRect(hwnd, &rect);

        // Draw flat background
        FillRect(hdc, &rect, hBackgroundBrush);

        // Draw 1px splitter
        DrawSplitter(hdc, &rect);

        EndPaint(hwnd, &ps);
        return 0;
    }

    case WM_COMMAND:
        if (LOWORD(wParam) == 1001) // Filter edit box
        {
            if (HIWORD(wParam) == EN_CHANGE)
            {
                // Filter text changed - apply filter
                OnFilterTextChanged();
            }
        }
        return 0;

    case WM_NOTIFY:
    {
        LPNMHDR pnmh = (LPNMHDR)lParam;
        if (pnmh->hwndFrom == hwndLeft)
        {
            if (pnmh->code == LVN_GETDISPINFOW)
            {
                // 虚拟ListView数据请求
                NMLVDISPINFOW *pDispInfo = (NMLVDISPINFOW *)lParam;
                return HandleVirtualListViewNotify(pDispInfo);
            }
            else if (pnmh->code == LVN_ITEMCHANGED)
            {
                LPNMLISTVIEW pnmlv = (LPNMLISTVIEW)lParam;
                if (pnmlv->uNewState & LVIS_SELECTED)
                {
                    // File selection changed - update preview
                    PreviewSelectedFile();
                }
            }
            else if (pnmh->code == LVN_COLUMNCLICK)
            {
                // Column header clicked - implement sorting
                LPNMLISTVIEW pnmlv = (LPNMLISTVIEW)lParam;
                SortFileList(pnmlv->iSubItem);
            }
            else if (pnmh->code == NM_RCLICK)
            {
                // 右键点击处理
                LPNMITEMACTIVATE pnmia = (LPNMITEMACTIVATE)lParam;
                HandleListViewRightClick(pnmia);
            }
        }
        else if (pnmh->hwndFrom == ListView_GetHeader(hwndLeft))
        {
            if (pnmh->code == HDN_ENDTRACK || pnmh->code == HDN_ENDTRACKW)
            {
                // Column width changed - save new widths
                SaveColumnSettings();
            }
            else if (pnmh->code == HDN_ENDDRAG)
            {
                // Column order changed - save new order
                SaveColumnSettings();
            }
        }
        return 0;
    }

    case WM_LBUTTONDOWN:
    {
        int x = LOWORD(lParam);
        int y = HIWORD(lParam);

        // Check if clicking on splitter (1px wide)
        if (x >= splitterPos && x <= splitterPos + SPLITTER_WIDTH && y >= INPUT_HEIGHT)
        {
            isDragging = TRUE;
            SetCapture(hwnd);
            SetCursor(LoadCursor(NULL, IDC_SIZEWE));
        }
        return 0;
    }

    case WM_LBUTTONUP:
        if (isDragging)
        {
            isDragging = FALSE;
            ReleaseCapture();
            SaveConfiguration(); // Save splitter position
        }
        return 0;

    case WM_MOUSEMOVE:
    {
        int x = LOWORD(lParam);
        int y = HIWORD(lParam);

        if (isDragging)
        {
            // Update splitter position
            RECT rect;
            GetClientRect(hwnd, &rect);

            if (x > 200 && x < rect.right - 200) // Min width constraints
            {
                splitterPos = x;
                UpdateLayout(hwnd);
                InvalidateRect(hwnd, NULL, TRUE);
            }
        }
        else if (x >= splitterPos && x <= splitterPos + SPLITTER_WIDTH && y >= INPUT_HEIGHT)
        {
            // Show resize cursor over splitter
            SetCursor(LoadCursor(NULL, IDC_SIZEWE));
        }
        else
        {
            SetCursor(LoadCursor(NULL, IDC_ARROW));
        }
        return 0;
    }

    case WM_USER + 1: // Refresh UI after USN scan
        // USN扫描完成后，延迟加载线程会自动处理数据加载和渲染
        UpdateStatusBar();
        return 0;

    case WM_USER + 2: // Refresh UI after file change
        // 文件变化时，延迟加载线程会重新加载数据
        UpdateStatusBar();
        return 0;

    case WM_USER + 5: // One-time render from delayed loading thread

        // 检查USN数据状态并渲染
        if (!g_dataLoaded && g_usnFileCount > 0 && g_usnFiles != NULL)
        {
            // 额外的安全检查
            if (IsWindow(hwndLeft))
            {
                RenderUSNFileTree();
            }
        }
        return 0;

    case WM_USER + 6: // 筛选完成消息
        // 更新虚拟ListView项目数量
        if (hwndLeft && IsWindow(hwndLeft))
        {
            ListView_SetItemCount(hwndLeft, g_virtualItemCount);
            InvalidateRect(hwndLeft, NULL, FALSE);
        }

        // 更新状态栏
        UpdateStatusBar();
        return 0;

    case WM_USER + 7: // 排序完成
        // 刷新ListView显示
        if (hwndLeft && IsWindow(hwndLeft))
        {
            ListView_SetItemCount(hwndLeft, g_usnFileCount);
            InvalidateRect(hwndLeft, NULL, TRUE);
        }
        return 0;

    case WM_DPICHANGED: // DPI变化处理
        HandleDPIChanged(hwnd, wParam, lParam);
        return 0;

    case WM_USER + 10: // 重新布局（DPI变化后）
        UpdateLayout(hwnd);
        // 重新创建字体
        if (hwndLeft && IsWindow(hwndLeft))
        {
            HFONT hNewFont = CreateScaledFont(16, L"Microsoft YaHei");
            if (hNewFont)
            {
                SendMessage(hwndLeft, WM_SETFONT, (WPARAM)hNewFont, TRUE);
            }
        }
        if (hwndEdit && IsWindow(hwndEdit))
        {
            HFONT hNewFont = CreateScaledFont(16, L"Microsoft YaHei");
            if (hNewFont)
            {
                SendMessage(hwndEdit, WM_SETFONT, (WPARAM)hNewFont, TRUE);
            }
        }
        InvalidateRect(hwnd, NULL, TRUE);
        return 0;

    case WM_DESTROY:
        // Save configuration before exit
        SaveConfiguration();

        // Stop background threads
        if (g_usnReadThread)
        {
            WaitForSingleObject(g_usnReadThread, 5000);
            CloseHandle(g_usnReadThread);
            g_usnReadThread = NULL;
        }
        StopFileWatcher();

        // 数据库功能已移除

        // Clean up USN memory
        if (g_usnFiles)
        {
            free(g_usnFiles);
            g_usnFiles = NULL;
        }
        DeleteCriticalSection(&g_usnDataCriticalSection);

        // Clean up resources
        if (hFont)
            DeleteObject(hFont);
        if (hBackgroundBrush)
            DeleteObject(hBackgroundBrush);
        if (hSplitterBrush)
            DeleteObject(hSplitterBrush);

        // 清理筛选相关内存
        CleanupFilterBitmap();

        // 清理临界区
        DeleteCriticalSection(&g_dataCriticalSection);

        PostQuitMessage(0);
        return 0;

    default:
        return DefWindowProc(hwnd, uMsg, wParam, lParam);
    }
}

// Main function
int main(int argc, char *argv[])
{
    // 第一步：初始化DPI感知（必须在创建任何窗口之前）
    InitializeDPIAwareness();

    // 不分配控制台窗口，程序以纯GUI模式运行
    // AllocConsole(); // 注释掉控制台分配
    // freopen("CONOUT$", "w", stdout);
    // freopen("CONOUT$", "w", stderr);

    // 初始化临界区
    InitializeCriticalSection(&g_dataCriticalSection);

    // Set UTF-8 support for Chinese characters
    setlocale(LC_ALL, "");       // 使用系统默认locale
    SetConsoleOutputCP(CP_UTF8); // 设置控制台输出为UTF-8
    SetConsoleCP(CP_UTF8);       // 设置控制台输入为UTF-8

    // 控制台窗口已禁用，程序以纯GUI模式运行
    // HWND consoleWindow = GetConsoleWindow();
    // if (consoleWindow != NULL)
    // {
    //     ShowWindow(consoleWindow, SW_SHOW);
    // }

    // Initialize common controls
    INITCOMMONCONTROLSEX icex;
    icex.dwSize = sizeof(INITCOMMONCONTROLSEX);
    icex.dwICC = ICC_LISTVIEW_CLASSES | ICC_BAR_CLASSES;

    if (!InitCommonControlsEx(&icex))
    {
        MessageBoxW(NULL, L"Failed to initialize common controls", L"Error", MB_OK);
        return 1;
    }

    // Get instance handle
    HINSTANCE hInstance = GetModuleHandle(NULL);

    // Register window class
    const WCHAR *szWindowClass = L"CompleteFilePreviewerClass";
    WNDCLASSW wc = {0};
    wc.lpfnWndProc = WindowProc;
    wc.hInstance = hInstance;
    wc.hbrBackground = NULL; // We'll handle painting ourselves
    wc.lpszClassName = szWindowClass;
    wc.hCursor = LoadCursor(NULL, IDC_ARROW);
    // 使用嵌入的资源图标
    wc.hIcon = LoadIcon(hInstance, MAKEINTRESOURCE(IDI_MAIN_ICON));

    if (!RegisterClassW(&wc))
    {
        MessageBoxW(NULL, L"Window Registration Failed!", L"Error", MB_OK);
        return 1;
    }

    // 初始化USN内存
    InitializeUSNMemory();

    // 初始化筛选线程
    InitializeFilterThread();

    // 初始化筛选位图
    InitializeFilterBitmap();

    // 加载配置和扫描状态
    LoadConfiguration(); // config.ini (includes LoadScanStatus)

    // Determine window position and size from configuration
    int windowX = (g_config.windowX > 0) ? g_config.windowX : CW_USEDEFAULT;
    int windowY = (g_config.windowY > 0) ? g_config.windowY : CW_USEDEFAULT;
    int windowWidth = (g_config.windowWidth > 0) ? g_config.windowWidth : DEFAULT_WINDOW_WIDTH;
    int windowHeight = (g_config.windowHeight > 0) ? g_config.windowHeight : DEFAULT_WINDOW_HEIGHT;

    // Create main window
    hwndMain = CreateWindowW(
        szWindowClass,
        L"EveryView",
        WS_OVERLAPPEDWINDOW | WS_CLIPCHILDREN,
        windowX, windowY,
        windowWidth, windowHeight,
        NULL, NULL, hInstance, NULL);

    if (!hwndMain)
    {
        DWORD error = GetLastError();
        MessageBoxW(NULL, L"Window Creation Failed!", L"Error", MB_OK);
        return 1;
    }

    // 初始化DPI设置
    UpdateDPISettings(hwndMain);

    // 为窗口设置嵌入的资源图标
    HICON hWindowIcon = LoadIcon(hInstance, MAKEINTRESOURCE(IDI_MAIN_ICON));
    if (hWindowIcon != NULL)
    {
        SendMessage(hwndMain, WM_SETICON, ICON_BIG, (LPARAM)hWindowIcon);
        SendMessage(hwndMain, WM_SETICON, ICON_SMALL, (LPARAM)hWindowIcon);
    }

    // 动态设置
    SetWindowTextW(hwndMain, L"EveryView");

    // Show window
    ShowWindow(hwndMain, SW_SHOW);
    UpdateWindow(hwndMain);

    // Ensure window is brought to foreground
    SetForegroundWindow(hwndMain);
    BringWindowToTop(hwndMain);
    SetFocus(hwndMain);

    // ====== 等待窗口渲染完成后启动USN读取 ======
    StartUSNReadThread();
    // ==========================

    // Message loop
    MSG msg;
    while (GetMessage(&msg, NULL, 0, 0))
    {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }

    // 清理筛选线程
    CleanupFilterThread();

    // 清理USN内存
    CleanupUSNMemory();

    return (int)msg.wParam;
}

// 排序比较函数
int CompareUSNFiles(const void *a, const void *b)
{
    const USNFileData *fileA = (const USNFileData *)a;
    const USNFileData *fileB = (const USNFileData *)b;
    int result = 0;

    switch (g_sortColumn)
    {
    case 0: // 名称列
        result = _wcsicmp(fileA->fileName, fileB->fileName);
        break;
    case 1: // 路径列
        result = _wcsicmp(fileA->fullPath, fileB->fullPath);
        break;
    case 2: // 大小列 - 特殊处理文件夹
        // 文件夹排在最前面（小到大排序时）
        if (fileA->isDirectory && !fileB->isDirectory)
            result = -1; // A是文件夹，B是文件，A排前面
        else if (!fileA->isDirectory && fileB->isDirectory)
            result = 1; // A是文件，B是文件夹，B排前面
        else if (fileA->isDirectory && fileB->isDirectory)
            result = _wcsicmp(fileA->fileName, fileB->fileName); // 都是文件夹，按名称排序
        else
        {
            // 都是文件，按大小排序
            if (fileA->fileSize < fileB->fileSize)
                result = -1;
            else if (fileA->fileSize > fileB->fileSize)
                result = 1;
            else
                result = 0;
        }
        break;
    case 3: // 修改时间列
        if (fileA->lastWriteTime < fileB->lastWriteTime)
            result = -1;
        else if (fileA->lastWriteTime > fileB->lastWriteTime)
            result = 1;
        else
            result = 0;
        break;
    default:
        result = 0;
        break;
    }

    // 如果是降序，反转结果
    if (!g_sortAscending)
        result = -result;

    return result;
}

// 反转文件列表（用于快速切换排序方向）
void ReverseFileList()
{
    if (g_usnFileCount <= 1)
        return;

    USNFileData temp;
    int start = 0;
    int end = g_usnFileCount - 1;

    while (start < end)
    {
        // 交换元素
        temp = g_usnFiles[start];
        g_usnFiles[start] = g_usnFiles[end];
        g_usnFiles[end] = temp;

        start++;
        end--;
    }
}

// 排序线程参数结构
typedef struct
{
    int column;
    BOOL ascending;
} SortThreadParam;

// 排序线程函数
DWORD WINAPI SortThreadProc(LPVOID lpParam)
{
    SortThreadParam *param = (SortThreadParam *)lpParam;

    // 进入临界区进行排序
    EnterCriticalSection(&g_usnDataCriticalSection);

    g_sortColumn = param->column;
    g_sortAscending = param->ascending;

    // 使用qsort进行排序
    qsort(g_usnFiles, g_usnFileCount, sizeof(USNFileData), CompareUSNFiles);

    LeaveCriticalSection(&g_usnDataCriticalSection);

    // 通知主线程排序完成
    if (hwndMain && IsWindow(hwndMain))
    {
        PostMessage(hwndMain, WM_USER + 7, 0, 0); // 自定义消息：排序完成
    }

    g_sortInProgress = FALSE;
    free(param);

    return 0;
}

// 对筛选结果进行排序
void SortFilteredResults(int column)
{
    if (!g_filterActive || !g_filterBitmap || g_filteredCount == 0)
    {
        return;
    }

    // 创建筛选文件的临时数组
    USNFileData *filteredFiles = malloc(g_filteredCount * sizeof(USNFileData));
    if (!filteredFiles)
    {
        return;
    }

    // 提取筛选的文件到临时数组
    int filteredIndex = 0;
    for (int i = 0; i < g_usnFileCount && filteredIndex < g_filteredCount; i++)
    {
        if (GetFilterBit(i))
        {
            filteredFiles[filteredIndex] = g_usnFiles[i];
            filteredIndex++;
        }
    }

    // 设置排序参数并排序临时数组
    g_sortColumn = column;
    qsort(filteredFiles, g_filteredCount, sizeof(USNFileData), CompareUSNFiles);

    // 将排序后的结果写回原数组的相应位置
    filteredIndex = 0;
    for (int i = 0; i < g_usnFileCount && filteredIndex < g_filteredCount; i++)
    {
        if (GetFilterBit(i))
        {
            g_usnFiles[i] = filteredFiles[filteredIndex];
            filteredIndex++;
        }
    }

    free(filteredFiles);
}

// 排序文件列表（优化版本，支持筛选）
void SortFileList(int column)
{
    if (g_usnFileCount == 0)
        return;

    // 防止重复排序
    if (g_sortInProgress)
    {
        return;
    }

    BOOL newAscending = TRUE;

    // 如果点击的是同一列，切换排序方向
    if (g_sortColumn == column)
    {
        newAscending = !g_sortAscending;
    }
    else
    {
        // 新列，默认升序
        newAscending = TRUE;
    }

    g_sortAscending = newAscending;

    // 检查是否有活动筛选
    if (g_filterActive && g_filterBitmap && g_filteredCount > 0)
    {

        EnterCriticalSection(&g_dataCriticalSection);
        SortFilteredResults(column);
        LeaveCriticalSection(&g_dataCriticalSection);

        // 刷新ListView显示
        if (hwndLeft && IsWindow(hwndLeft))
        {
            ListView_SetItemCount(hwndLeft, g_virtualItemCount);
            InvalidateRect(hwndLeft, NULL, TRUE);
        }

        return;
    }

    // 没有筛选，对所有文件进行排序

    // 对于大数据集，使用后台线程
    if (g_usnFileCount >= 100000)
    {
        g_sortInProgress = TRUE;

        SortThreadParam *param = malloc(sizeof(SortThreadParam));
        param->column = column;
        param->ascending = newAscending;

        g_sortThread = CreateThread(NULL, 0, SortThreadProc, param, 0, NULL);
        if (g_sortThread == NULL)
        {
            g_sortInProgress = FALSE;
            free(param);
        }
    }
    else
    {
        // 小数据集直接在主线程排序
        EnterCriticalSection(&g_usnDataCriticalSection);

        g_sortColumn = column;
        qsort(g_usnFiles, g_usnFileCount, sizeof(USNFileData), CompareUSNFiles);

        LeaveCriticalSection(&g_usnDataCriticalSection);

        // 刷新ListView显示
        if (hwndLeft && IsWindow(hwndLeft))
        {
            ListView_SetItemCount(hwndLeft, g_usnFileCount);
            InvalidateRect(hwndLeft, NULL, TRUE);
        }
    }
}
