二、监控文件的变化
监控文件的变化在这里我采用的是ReadDirectoryChangesW方法进行监控。ReadDirectoryChangesW function (winbase.h) - Win32 apps | Microsoft Docs

文件操作类型有四种，分别为 创建、更改、删除、重命名

对应到Windows API里是FILE_ACTION_ADDED、FILE_ACTION_MODIFIED、FILE_ACTION_REMOVED、FILE_ACTION_RENAMED_OLD_NAME。

通过监控这四个操作，我们可以实现监控文件的变化，并更新数据库索引。

void monitor_path(const char* path)
{
	DWORD cb_bytes;
	char file_name[1000]; //设置文件名
	char file_rename[1000]; //设置文件重命名后的名字;
	char _path[1000];
	char notify[1024];

	memset(_path, 0, 1000);
	strcpy_s(_path, 1000, path);

	WCHAR _dir[1000];
	memset(_dir, 0, sizeof(_dir));
	MultiByteToWideChar(CP_ACP, 0, _path, 1000, _dir,
	                    sizeof(_dir) / sizeof(_dir[0]));

	HANDLE dirHandle = CreateFile(_dir,
	                              GENERIC_READ | GENERIC_WRITE | FILE_LIST_DIRECTORY,
	                              FILE_SHARE_READ | FILE_SHARE_WRITE,
	                              nullptr,
	                              OPEN_EXISTING,
	                              FILE_FLAG_BACKUP_SEMANTICS,
	                              nullptr);

	if (dirHandle == INVALID_HANDLE_VALUE) //若网络重定向或目标文件系统不支持该操作，函数失败，同时调用GetLastError()返回ERROR_INVALID_FUNCTION
	{
		cout << "error " << GetLastError() << endl;
		exit(0);
	}

	auto* pnotify = reinterpret_cast<FILE_NOTIFY_INFORMATION*>(notify);

	while (is_running)
	{
		if (ReadDirectoryChangesW(dirHandle, &notify, 1024, true,
		                          FILE_NOTIFY_CHANGE_FILE_NAME | FILE_NOTIFY_CHANGE_DIR_NAME | FILE_NOTIFY_CHANGE_SIZE,
		                          &cb_bytes, nullptr, nullptr))
		{
			//转换文件名为多字节字符串;
			if (pnotify->FileName)
			{
				memset(file_name, 0, sizeof(file_name));
				memset(fileName, 0, sizeof(fileName));
				wcscpy_s(fileName, pnotify->FileName);
				WideCharToMultiByte(CP_ACP, 0, pnotify->FileName, pnotify->FileNameLength / 2, file_name, 250, nullptr,
				                    nullptr);
			}

			//获取重命名的文件名;
			if (pnotify->NextEntryOffset != 0 && (pnotify->FileNameLength > 0 && pnotify->FileNameLength < 1000))
			{
				auto p = reinterpret_cast<PFILE_NOTIFY_INFORMATION>(reinterpret_cast<char*>(pnotify) + pnotify->
					NextEntryOffset);
				memset(file_rename, 0, 1000);
				memset(fileRename, 0, 1000);
				wcscpy_s(fileRename, pnotify->FileName);
				WideCharToMultiByte(CP_ACP, 0, p->FileName, p->FileNameLength / 2, file_rename, 250, nullptr, nullptr);
			}

			if (file_name[strlen(file_name) - 1] == '~')
			{
				file_name[strlen(file_name) - 1] = '\0';
			}
			if (file_rename[strlen(file_rename) - 1] == '~')
			{
				file_rename[strlen(file_rename) - 1] = '\0';
			}

			//设置类型过滤器,监听文件创建、更改、删除、重命名等;
			switch (pnotify->Action)
			{
			case FILE_ACTION_ADDED:
				if (strstr(file_name, "$RECYCLE.BIN") == nullptr)
				{
					string data;
					data.append(_path);
					data.append(file_name);
#ifdef TEST
                    cout << "file add : " << data << endl;
#endif
					add_record(to_utf8(StringToWString(data)));
				}
				break;

			case FILE_ACTION_MODIFIED:
				if (strstr(file_name, "$RECYCLE.BIN") == nullptr && strstr(file_name, "fileAdded.txt") == nullptr &&
					strstr(file_name, "fileRemoved.txt") == nullptr)
				{
					string data;
					data.append(_path);
					data.append(file_name);
#ifdef TEST
                    cout << "file add : " << data << endl;
#endif
					add_record(to_utf8(StringToWString(data)));
				}
				break;

			case FILE_ACTION_REMOVED:
				if (strstr(file_name, "$RECYCLE.BIN") == nullptr)
				{
					string data;
					data.append(_path);
					data.append(file_name);
#ifdef TEST
                    cout << "file removed : " << data << endl;
#endif
					delete_record(to_utf8(StringToWString(data)));
				}
				break;

			case FILE_ACTION_RENAMED_OLD_NAME:
				if (strstr(file_name, "$RECYCLE.BIN") == nullptr)
				{
					string data;
					data.append(_path);
					data.append(file_name);

					delete_record(to_utf8(StringToWString(data)));

					data.clear();
					data.append(_path);
					data.append(file_rename);
#ifdef TEST
                    cout << "file renamed : " << data << "->" << data << endl;
#endif
					add_record(to_utf8(StringToWString(data)));
				}
				break;

			default:
				cout << "Unknown command!" << endl;
			}
		}
	}
	CloseHandle(dirHandle);
	cout << "stop monitoring " << _path << endl;
	return;
}
至此，实现Everything第二步，监控文件变化完成。