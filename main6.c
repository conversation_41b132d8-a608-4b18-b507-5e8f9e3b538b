
#include <windows.h>
#include <windowsx.h> // 用于GET_X_LPARAM和GET_Y_LPARAM
#include <commctrl.h>
#include <stdio.h> // 用于sprintf函数
#include <stdlib.h>
#pragma comment(lib, "comctl32.lib")
#pragma comment(lib, "gdi32.lib")

// 添加通用控件清单
#if defined _M_IX86
#pragma comment(linker, "/manifestdependency:\"type='win32' name='Microsoft.Windows.Common-Controls' version='6.0.0.0' processorArchitecture='x86' publicKeyToken='6595b64144ccf1df' language='*'\"")
#elif defined _M_IA64
#pragma comment(linker, "/manifestdependency:\"type='win32' name='Microsoft.Windows.Common-Controls' version='6.0.0.0' processorArchitecture='ia64' publicKeyToken='6595b64144ccf1df' language='*'\"")
#elif defined _M_X64
#pragma comment(linker, "/manifestdependency:\"type='win32' name='Microsoft.Windows.Common-Controls' version='6.0.0.0' processorArchitecture='amd64' publicKeyToken='6595b64144ccf1df' language='*'\"")
#else
#pragma comment(linker, "/manifestdependency:\"type='win32' name='Microsoft.Windows.Common-Controls' version='6.0.0.0' processorArchitecture='*' publicKeyToken='6595b64144ccf1df' language='*'\"")
#endif

// 定义控件ID
#define ID_EDIT 1001
#define ID_LEFT 1002
#define ID_RIGHT 1003
#define ID_STATUSBAR 1004
#define SPLITTER_WIDTH 15
#define MIN_PANE_WIDTH 100 // 最小窗格宽度
#define SPLIT_WIDTH 1      // 分割线宽度

// 全局变量声明
static HWND hwndEdit = NULL;         // 筛选输入框
static HWND hwndLeft = NULL;         // 左侧显示框
static HWND hwndRight = NULL;        // 右侧显示框
static HWND hwndSplitter = NULL;     // 分割条
static HWND hwndStatus = NULL;       // 状态栏句柄
static BOOL isDragging = FALSE;      // 是否正在拖动分割条
static HCURSOR hSizeWECursor = NULL; // 静态光标句柄
static int splitterPos = 300;        // 分割条位置
static int dragStartX = 0;           // 拖拽开始位置
static int originalSplitterPos = 0;  // 拖拽开始时的分割线位置
static BOOL configLoaded = FALSE;    // 配置是否已加载
static BOOL isLoadingConfig = FALSE; // 是否正在加载配置
static BOOL wasMaximized = FALSE;    // 上次是否为最大化状态

#define STATUS_HEIGHT 20
#define CONFIG_FILE ".\\config.ini"
static HBRUSH hBackBrush = NULL;     // 背景画刷
static HBRUSH hEditBrush = NULL;     // 编辑框背景画刷
static HBRUSH hSplitterBrush = NULL; // 分割条画刷
static HBRUSH hFlatBrush = NULL;     // 扁平风格画刷
static HFONT hFont = NULL;           // 全局字体 - 微软雅黑 16号
static HFONT hStatusFont = NULL;     // 状态栏字体
static WNDPROC oldEditProc = NULL;   // 原始编辑框过程

// 函数声明
LRESULT CALLBACK WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam);
LRESULT CALLBACK EditProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam);
LRESULT CALLBACK DisplayProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam);
void UpdateLayout(HWND hwnd);
BOOL IsMouseOverSplitter(int mouseX, int mouseY); // 检测鼠标是否在分割线上

// 配置文件相关函数 - 重新设计的架构
void LoadWindowConfig(HWND hwnd); // 只加载窗口位置和大小
void LoadUIConfig(HWND hwnd);     // 加载UI配置（分割线、文件树）
void SaveWindowConfig(HWND hwnd); // 保存窗口配置
void SaveUIConfig(HWND hwnd);     // 保存UI配置
void SaveAllConfig(HWND hwnd);    // 保存所有配置

// 分离的配置函数
void LoadSplitterConfig();
void SaveSplitterConfig();
void LoadFileTreeConfig(HWND hwndListView);
void SaveFileTreeConfig(HWND hwndListView);

// 配置验证和修复函数
void ValidateAndFixSplitterPosition(HWND hwnd);
void ValidateAndFixWindowPosition(HWND hwnd);

// 保存原始窗口过程
static WNDPROC oldLeftProc = NULL;
static WNDPROC oldRightProc = NULL;

// EditProc函数实现 - 筛选输入框自定义处理
LRESULT CALLBACK EditProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam)
{
    switch (uMsg)
    {
    case WM_NCPAINT:
    case WM_PAINT:
    {
        LRESULT result = CallWindowProc(oldEditProc, hwnd, uMsg, wParam, lParam);
        if (uMsg == WM_PAINT)
        {
            HDC hdc = GetDC(hwnd);
            RECT rect;
            GetClientRect(hwnd, &rect);

            // 绘制底部边框线
            HPEN hPen = CreatePen(PS_SOLID, 1, RGB(200, 200, 200));
            HPEN hOldPen = SelectObject(hdc, hPen);
            MoveToEx(hdc, rect.left, rect.bottom - 1, NULL);
            LineTo(hdc, rect.right, rect.bottom - 1);

            SelectObject(hdc, hOldPen);
            DeleteObject(hPen);
            ReleaseDC(hwnd, hdc);
        }
        return result;
    }

    case WM_SETFONT:
    {
        // 设置字体后调整文字位置
        LRESULT result = CallWindowProc(oldEditProc, hwnd, uMsg, wParam, lParam);

        // 设置文字边距以实现上下居中，左对齐
        RECT margins;
        margins.left = 4;   // 左边距4px，实现靠左
        margins.top = 2;    // 上边距2px，配合下边距实现上下居中
        margins.right = 4;  // 右边距4px
        margins.bottom = 2; // 下边距2px，配合上边距实现上下居中

        SendMessage(hwnd, EM_SETMARGINS, EC_LEFTMARGIN | EC_RIGHTMARGIN,
                    MAKELPARAM(margins.left, margins.right));

        return result;
    }
    }
    return CallWindowProc(oldEditProc, hwnd, uMsg, wParam, lParam);
}

// DisplayProc函数实现 - 用于左右显示窗口
LRESULT CALLBACK DisplayProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam)
{
    WNDPROC originalProc = NULL;

    // 确定使用哪个原始窗口过程
    if (hwnd == hwndLeft)
        originalProc = oldLeftProc;
    else if (hwnd == hwndRight)
        originalProc = oldRightProc;

    switch (uMsg)
    {
    case WM_SETFOCUS:
        // 阻止显示窗口获得焦点，将焦点重定向到筛选输入框
        if (hwndEdit)
        {
            SetFocus(hwndEdit);
        }
        return 0;

    case WM_LBUTTONDOWN:
    case WM_RBUTTONDOWN:
    case WM_MBUTTONDOWN:
        // 点击显示窗口时，将焦点设置到筛选输入框
        if (hwndEdit)
        {
            SetFocus(hwndEdit);
        }
        // 继续处理原始消息
        break;

    case WM_KEYDOWN:
    case WM_KEYUP:
    case WM_CHAR:
        // 将键盘输入重定向到筛选输入框
        if (hwndEdit)
        {
            SetFocus(hwndEdit);
            return SendMessage(hwndEdit, uMsg, wParam, lParam);
        }
        return 0;
    }

    // 调用原始窗口过程
    if (originalProc)
        return CallWindowProc(originalProc, hwnd, uMsg, wParam, lParam);
    else
        return DefWindowProc(hwnd, uMsg, wParam, lParam);
}

// 检测鼠标是否在分割线上
BOOL IsMouseOverSplitter(int mouseX, int mouseY)
{
    const int editHeight = 24;
    return (abs(mouseX - splitterPos) <= SPLITTER_WIDTH && mouseY >= editHeight);
}

void UpdateLayout(HWND hwnd)
{
    RECT rcClient;
    GetClientRect(hwnd, &rcClient);

    // 获取状态栏高度
    int statusHeight = 0;
    if (hwndStatus)
    {
        RECT rcStatus;
        GetWindowRect(hwndStatus, &rcStatus);
        statusHeight = rcStatus.bottom - rcStatus.top;
        SendMessage(hwndStatus, WM_SIZE, 0, 0);
    }

    // 设置控件间距和高度 - 扁平风格，0px间隙
    const int margin = 0;               // 0px间隙
    const int editHeight = 24;          // 编辑框高度：行高20 + 上下边距4px
    const int splitWidth = SPLIT_WIDTH; // 分割线宽度
    const int borderWidth = 1;          // 1px边框宽度

    // 确保分割线位置在有效范围内
    int minSplitPos = MIN_PANE_WIDTH;                  // 左侧最小宽度
    int maxSplitPos = rcClient.right - MIN_PANE_WIDTH; // 右侧最小宽度

    // 如果正在加载配置，不要调整分割线位置
    if (!isLoadingConfig)
    {
        // 只有在窗口太小或分割线位置明显无效时才调整
        if (maxSplitPos <= minSplitPos)
        {
            // 窗口太小，使用中央位置
            splitterPos = rcClient.right / 2;
        }
        else
        {
            // 如果配置已加载，更保守地调整分割线位置
            if (configLoaded)
            {
                // 只在分割线位置严重超出范围时才调整
                if (splitterPos < minSplitPos)
                    splitterPos = minSplitPos;
                else if (splitterPos > maxSplitPos)
                {
                    // 只有在严重超出范围时才调整到最大位置
                    splitterPos = maxSplitPos;
                }
            }
            else
            {
                // 配置未加载时，使用原来的逻辑
                if (splitterPos < minSplitPos)
                    splitterPos = minSplitPos;
                else if (splitterPos > maxSplitPos)
                    splitterPos = maxSplitPos;
            }
        }
    }

    // 计算各个控件的位置 - 0px间隙设计
    int availableHeight = rcClient.bottom - statusHeight;

    // 顶部编辑框 - 占满整个宽度，1px边框
    MoveWindow(hwndEdit, borderWidth, borderWidth,
               rcClient.right - 2 * borderWidth,
               editHeight - 2 * borderWidth, TRUE);

    // 左侧窗口 - 从编辑框下方开始，到分割线左侧，1px边框
    MoveWindow(hwndLeft, borderWidth, editHeight,
               splitterPos - splitWidth - borderWidth,
               availableHeight - editHeight - borderWidth, TRUE);

    // 右侧窗口 - 从分割线右侧开始，到窗口右边，1px边框
    MoveWindow(hwndRight, splitterPos + splitWidth,
               editHeight,
               rcClient.right - (splitterPos + splitWidth) - borderWidth,
               availableHeight - editHeight - borderWidth, TRUE);

    // 强制重绘整个窗口
    InvalidateRect(hwnd, NULL, TRUE);
}

// 主窗口过程
LRESULT CALLBACK WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam)
{
    switch (uMsg)
    {
    case WM_KEYDOWN:
    {
        if (isDragging)
        {
            if (wParam == VK_ESCAPE)
            {
                // ESC键取消拖拽，恢复原始位置
                splitterPos = originalSplitterPos;
                isDragging = FALSE;
                ReleaseCapture();

                // 恢复线程优先级
                SetThreadPriority(GetCurrentThread(), THREAD_PRIORITY_NORMAL);

                UpdateLayout(hwnd);

                if (hwndStatus)
                {
                    SendMessage(hwndStatus, SB_SETTEXT, 0, (LPARAM) "拖拽已取消，分割线位置已恢复");
                    SetTimer(hwnd, 1, 2000, NULL);
                }

                RedrawWindow(hwnd, NULL, NULL, RDW_INVALIDATE | RDW_UPDATENOW | RDW_ALLCHILDREN);
                return 0;
            }
            else if (wParam == VK_LEFT)
            {
                // 左箭头键微调分割线位置
                RECT rcClient;
                GetClientRect(hwnd, &rcClient);
                int newPos = splitterPos - 1;
                if (newPos >= MIN_PANE_WIDTH)
                {
                    splitterPos = newPos;
                    UpdateLayout(hwnd);
                    InvalidateRect(hwnd, NULL, TRUE);
                }
                return 0;
            }
            else if (wParam == VK_RIGHT)
            {
                // 右箭头键微调分割线位置
                RECT rcClient;
                GetClientRect(hwnd, &rcClient);
                int newPos = splitterPos + 1;
                if (newPos <= rcClient.right - MIN_PANE_WIDTH)
                {
                    splitterPos = newPos;
                    UpdateLayout(hwnd);
                    InvalidateRect(hwnd, NULL, TRUE);
                }
                return 0;
            }
        }
        break;
    }
    case WM_SETFOCUS:
        if (hwndEdit)
            SetFocus(hwndEdit);
        return 0;

    case WM_COMMAND:
    {
        // 处理控件消息
        WORD wNotifyCode = HIWORD(wParam);
        WORD wID = LOWORD(wParam);
        HWND hwndCtl = (HWND)lParam;

        // 如果左右窗口试图获得焦点，将焦点重定向到筛选输入框
        if (hwndCtl == hwndLeft || hwndCtl == hwndRight)
        {
            if (wNotifyCode == EN_SETFOCUS)
            {
                SetFocus(hwndEdit);
                return 0;
            }
        }

        // 处理状态栏消息
        if (wID == ID_STATUSBAR)
        {
            return 0;
        }

        break;
    }

    case WM_NCCREATE:
        return TRUE;

    case WM_NCCALCSIZE:
        if (wParam)
        {
            return DefWindowProc(hwnd, uMsg, wParam, lParam);
        }
        break;

    case WM_CREATE:
    {
        // 初始化分割线位置为默认值
        splitterPos = 300;
        configLoaded = FALSE;

        // 创建扁平风格的字体 - 微软雅黑 16号，行高20
        hFont = CreateFont(
            16,                          // 字体高度 16px
            0,                           // 字体宽度 (自动)
            0,                           // 字体角度
            0,                           // 基线角度
            FW_NORMAL,                   // 字体粗细
            FALSE,                       // 斜体
            FALSE,                       // 下划线
            FALSE,                       // 删除线
            DEFAULT_CHARSET,             // 字符集
            OUT_DEFAULT_PRECIS,          // 输出精度
            CLIP_DEFAULT_PRECIS,         // 裁剪精度
            CLEARTYPE_QUALITY,           // 输出质量
            DEFAULT_PITCH | FF_DONTCARE, // 字体间距和族
            "Microsoft YaHei"            // 字体名称
        );

        // 创建状态栏字体 - 微软雅黑 12号
        hStatusFont = CreateFont(
            16, 0, 0, 0, FW_NORMAL, FALSE, FALSE, FALSE,
            DEFAULT_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS,
            CLEARTYPE_QUALITY, DEFAULT_PITCH | FF_DONTCARE,
            "Microsoft YaHei");

        // 创建扁平风格的画刷
        hEditBrush = CreateSolidBrush(RGB(255, 255, 255));     // 白色背景
        hBackBrush = CreateSolidBrush(RGB(240, 240, 240));     // 浅灰背景
        hSplitterBrush = CreateSolidBrush(RGB(200, 200, 200)); // 分割线颜色
        hFlatBrush = CreateSolidBrush(RGB(250, 250, 250));     // 扁平背景

        // 创建状态栏
        hwndStatus = CreateWindowEx(
            0,
            STATUSCLASSNAME,
            NULL,
            WS_CHILD | WS_VISIBLE | SBARS_SIZEGRIP,
            0, 0, 0, 0,
            hwnd,
            (HMENU)ID_STATUSBAR,
            ((LPCREATESTRUCT)lParam)->hInstance,
            NULL);

        // 设置状态栏字体
        if (hwndStatus && hStatusFont)
        {
            SendMessage(hwndStatus, WM_SETFONT, (WPARAM)hStatusFont, TRUE);
        }

        // 创建编辑框（扁平风格）
        hwndEdit = CreateWindowEx(
            0, // 移除边框实现扁平风格
            "EDIT",
            NULL,
            WS_CHILD | WS_VISIBLE | ES_AUTOHSCROLL,
            0, 0, 0, 0,
            hwnd,
            (HMENU)ID_EDIT,
            ((LPCREATESTRUCT)lParam)->hInstance,
            NULL);

        // 创建左侧文件树视图（4列：Name, Path, Size, Date modified）
        hwndLeft = CreateWindowEx(
            0, // 扁平风格
            WC_LISTVIEW,
            NULL,
            WS_CHILD | WS_VISIBLE | LVS_REPORT | LVS_SINGLESEL |
                LVS_SHOWSELALWAYS, // 移除LVS_NOSORTHEADER以允许列调整
            0, 0, 0, 0,
            hwnd,
            (HMENU)ID_LEFT,
            ((LPCREATESTRUCT)lParam)->hInstance,
            NULL);

        // 创建右侧窗口（扁平风格，只读显示）
        hwndRight = CreateWindowEx(
            0, // 移除边框实现扁平风格
            "EDIT",
            NULL,
            WS_CHILD | WS_VISIBLE |
                ES_MULTILINE | ES_AUTOVSCROLL | ES_AUTOHSCROLL | ES_READONLY,
            0, 0, 0, 0,
            hwnd,
            (HMENU)ID_RIGHT,
            ((LPCREATESTRUCT)lParam)->hInstance,
            NULL);

        // 设置ListView的扩展样式，包括列头拖拽
        ListView_SetExtendedListViewStyle(hwndLeft,
                                          LVS_EX_FULLROWSELECT | LVS_EX_GRIDLINES | LVS_EX_DOUBLEBUFFER |
                                              LVS_EX_HEADERDRAGDROP); // 允许列头拖拽重排序

        // 创建ListView的列
        LVCOLUMN lvc;
        lvc.mask = LVCF_TEXT | LVCF_WIDTH | LVCF_SUBITEM;

        // 第1列：Name
        lvc.iSubItem = 0;
        lvc.pszText = "Name";
        lvc.cx = 200;
        ListView_InsertColumn(hwndLeft, 0, &lvc);

        // 第2列：Path
        lvc.iSubItem = 1;
        lvc.pszText = "Path";
        lvc.cx = 300;
        ListView_InsertColumn(hwndLeft, 1, &lvc);

        // 第3列：Size
        lvc.iSubItem = 2;
        lvc.pszText = "Size";
        lvc.cx = 100;
        ListView_InsertColumn(hwndLeft, 2, &lvc);

        // 第4列：Date Modified
        lvc.iSubItem = 3;
        lvc.pszText = "Date Modified";
        lvc.cx = 150;
        ListView_InsertColumn(hwndLeft, 3, &lvc);

        // 设置所有控件的字体
        if (hFont)
        {
            SendMessage(hwndEdit, WM_SETFONT, (WPARAM)hFont, TRUE);
            SendMessage(hwndLeft, WM_SETFONT, (WPARAM)hFont, TRUE);
            SendMessage(hwndRight, WM_SETFONT, (WPARAM)hFont, TRUE);
        }

        // 注意：ListView的行高通过自定义绘制在WM_NOTIFY中控制

        // 子类化筛选输入框以实现自定义样式
        oldEditProc = (WNDPROC)SetWindowLongPtr(hwndEdit, GWLP_WNDPROC, (LONG_PTR)EditProc);

        // 设置筛选输入框的文字边距（靠左上下居中）
        SendMessage(hwndEdit, EM_SETMARGINS, EC_LEFTMARGIN | EC_RIGHTMARGIN,
                    MAKELPARAM(4, 4)); // 左右各4px边距

        // 禁用左右窗口的光标显示
        SendMessage(hwndLeft, EM_SETREADONLY, TRUE, 0);
        SendMessage(hwndRight, EM_SETREADONLY, TRUE, 0);

        // 设置左右窗口不可获得焦点
        SetWindowLong(hwndLeft, GWL_STYLE,
                      GetWindowLong(hwndLeft, GWL_STYLE) & ~WS_TABSTOP);
        SetWindowLong(hwndRight, GWL_STYLE,
                      GetWindowLong(hwndRight, GWL_STYLE) & ~WS_TABSTOP);

        // 注意：ListView不需要子类化DisplayProc，因为它有自己的焦点处理
        // 只对右侧窗口进行子类化
        oldRightProc = (WNDPROC)SetWindowLongPtr(hwndRight, GWLP_WNDPROC, (LONG_PTR)DisplayProc);

        // 添加示例数据到ListView
        LVITEM lvi;
        lvi.mask = LVIF_TEXT;
        lvi.iSubItem = 0;

        // 添加第1行数据
        lvi.iItem = 0;
        lvi.pszText = "document.txt";
        ListView_InsertItem(hwndLeft, &lvi);
        ListView_SetItemText(hwndLeft, 0, 1, "C:\\Users\\<USER>\\");
        ListView_SetItemText(hwndLeft, 0, 2, "1.2 KB");
        ListView_SetItemText(hwndLeft, 0, 3, "2024-01-15 14:30");

        // 添加第2行数据
        lvi.iItem = 1;
        lvi.pszText = "image.jpg";
        ListView_InsertItem(hwndLeft, &lvi);
        ListView_SetItemText(hwndLeft, 1, 1, "C:\\Users\\<USER>\\");
        ListView_SetItemText(hwndLeft, 1, 2, "256 KB");
        ListView_SetItemText(hwndLeft, 1, 3, "2024-01-14 09:15");

        // 添加第3行数据
        lvi.iItem = 2;
        lvi.pszText = "program.exe";
        ListView_InsertItem(hwndLeft, &lvi);
        ListView_SetItemText(hwndLeft, 2, 1, "C:\\Program Files\\App\\");
        ListView_SetItemText(hwndLeft, 2, 2, "2.5 MB");
        ListView_SetItemText(hwndLeft, 2, 3, "2024-01-13 16:45");

        // 添加示例文本到右侧显示窗口
        SetWindowText(hwndRight, "右侧详细信息窗口\r\n\r\n选择左侧文件查看详细信息：\r\n\r\n功能说明：\r\n- 文件属性显示\r\n- 预览内容\r\n- 元数据信息\r\n- 焦点始终在筛选输入框");

        // 设置默认焦点到筛选输入框
        SetFocus(hwndEdit);

        // 阶段2: 加载UI配置（分割线、文件树）
        LoadUIConfig(hwnd);

        // 显示配置加载状态
        if (hwndStatus)
        {
            RECT rcClient;
            GetClientRect(hwnd, &rcClient);
            int maxSplitPos = rcClient.right - MIN_PANE_WIDTH;

            char statusText[150];
            sprintf(statusText, "配置已加载 - 分割线位置: %d (窗口宽度: %d, 最大位置: %d)",
                    splitterPos, rcClient.right, maxSplitPos);
            SendMessage(hwndStatus, SB_SETTEXT, 0, (LPARAM)statusText);
            SetTimer(hwnd, 5, 5000, NULL); // 5秒后清除消息
        }
        return 0;
    }

    case WM_CTLCOLORSTATIC:
    {
        HDC hdcStatic = (HDC)wParam;
        HWND hwndStatic = (HWND)lParam;

        if (hwndStatic == hwndSplitter)
        {
            return (LRESULT)hSplitterBrush;
        }
        else if (hwndStatic == hwndRight)
        {
            SetBkColor(hdcStatic, RGB(255, 255, 255));
            SetTextColor(hdcStatic, RGB(51, 51, 51)); // 深灰色文字
            return (LRESULT)hEditBrush;
        }
        break;
    }

    case WM_CTLCOLOREDIT:
    {
        HDC hdcEdit = (HDC)wParam;
        HWND hwndCtl = (HWND)lParam;

        if (hwndCtl == hwndEdit)
        {
            // 筛选输入框 - 白色背景，深色文字
            SetBkColor(hdcEdit, RGB(255, 255, 255));
            SetTextColor(hdcEdit, RGB(51, 51, 51));
            return (LRESULT)hEditBrush;
        }
        else if (hwndCtl == hwndRight)
        {
            // 右侧显示窗口 - 浅色背景，深色文字
            SetBkColor(hdcEdit, RGB(250, 250, 250));
            SetTextColor(hdcEdit, RGB(51, 51, 51));
            return (LRESULT)hFlatBrush;
        }

        SetBkColor(hdcEdit, RGB(255, 255, 255));
        SetTextColor(hdcEdit, RGB(51, 51, 51));
        return (LRESULT)hEditBrush;
    }

    case WM_SIZE:
    {
        int width = LOWORD(lParam);
        int height = HIWORD(lParam);

        // 检测窗口状态变化
        BOOL isMaximized = (wParam == SIZE_MAXIMIZED);
        BOOL isRestored = (wParam == SIZE_RESTORED);

        // 如果窗口状态发生变化且配置已加载，保存当前配置
        if (configLoaded && (isMaximized != wasMaximized))
        {
            if (wasMaximized && isRestored)
            {
                // 从最大化恢复到正常状态，保存窗口配置
                SaveWindowConfig(hwnd);
            }
            wasMaximized = isMaximized;
        }

        // 更新状态栏
        if (hwndStatus)
        {
            SendMessage(hwndStatus, WM_SIZE, 0, 0);
        }

        // 更新窗口布局
        UpdateLayout(hwnd);
        return 0;
    }

    case WM_SETCURSOR:
    {
        if (LOWORD(lParam) == HTCLIENT)
        {
            POINT pt;
            GetCursorPos(&pt);
            ScreenToClient(hwnd, &pt);

            // 检查是否在分割线区域且在有效的垂直范围内
            if (abs(pt.x - splitterPos) <= SPLITTER_WIDTH || isDragging)
            {
                const int editHeight = 24; // 0px间隙设计
                if (pt.y >= editHeight)
                {
                    if (!hSizeWECursor)
                        hSizeWECursor = LoadCursor(NULL, IDC_SIZEWE);
                    SetCursor(hSizeWECursor);
                    return TRUE;
                }
            }
        }
        return DefWindowProc(hwnd, uMsg, wParam, lParam);
    }

    case WM_LBUTTONDOWN:
    {
        int xPos = GET_X_LPARAM(lParam);
        int yPos = GET_Y_LPARAM(lParam);

        // 简化的分割线检测：直接检查鼠标位置
        if (IsMouseOverSplitter(xPos, yPos))
        {
            // 立即开始拖动
            isDragging = TRUE;
            dragStartX = xPos;
            originalSplitterPos = splitterPos;

            SetCapture(hwnd);
            SetCursor(LoadCursor(NULL, IDC_SIZEWE));

            // 在状态栏显示拖拽提示
            if (hwndStatus)
            {
                char statusText[100];
                sprintf(statusText, "拖拽分割线调整窗格大小... 位置: %d", splitterPos);
                SendMessage(hwndStatus, SB_SETTEXT, 0, (LPARAM)statusText);
            }

            // 设置高优先级以确保响应性
            SetThreadPriority(GetCurrentThread(), THREAD_PRIORITY_ABOVE_NORMAL);
        }
        else
        {
            // 如果点击在其他区域，确保焦点在筛选输入框
            if (hwndEdit)
            {
                SetFocus(hwndEdit);
            }
        }
        return 0;
    }

    case WM_MOUSEMOVE:
    {
        int xPos = GET_X_LPARAM(lParam);
        int yPos = GET_Y_LPARAM(lParam);
        const int editHeight = 24;

        if (isDragging)
        {
            RECT rcClient;
            GetClientRect(hwnd, &rcClient);

            // 计算新的分割线位置
            int deltaX = xPos - dragStartX;
            int newSplitterPos = originalSplitterPos + deltaX;

            // 限制分割线位置在有效范围内
            int minSplitPos = MIN_PANE_WIDTH;
            int maxSplitPos = rcClient.right - MIN_PANE_WIDTH;

            if (newSplitterPos < minSplitPos)
                newSplitterPos = minSplitPos;
            if (newSplitterPos > maxSplitPos)
                newSplitterPos = maxSplitPos;

            // 只有当位置真正改变时才更新
            if (newSplitterPos != splitterPos)
            {
                splitterPos = newSplitterPos;

                // 立即更新布局
                UpdateLayout(hwnd);

                // 更新状态栏显示分割线位置
                if (hwndStatus)
                {
                    char statusText[100];
                    sprintf(statusText, "拖拽中... 位置: %d (偏移: %+d)", splitterPos, deltaX);
                    SendMessage(hwndStatus, SB_SETTEXT, 0, (LPARAM)statusText);
                }

                // 立即重绘
                InvalidateRect(hwnd, NULL, FALSE);
                UpdateWindow(hwnd);
            }

            // 确保光标保持为调整大小状态
            SetCursor(LoadCursor(NULL, IDC_SIZEWE));
        }
        else
        {
            // 检查鼠标是否在分割线附近
            if (IsMouseOverSplitter(xPos, yPos))
            {
                SetCursor(LoadCursor(NULL, IDC_SIZEWE));
            }
            else
            {
                SetCursor(LoadCursor(NULL, IDC_ARROW));
            }
        }
        return 0;
    }

    case WM_USER + 1: // 自定义消息：异步更新布局
    {
        if (isDragging)
        {
            UpdateLayout(hwnd);
        }
        return 0;
    }

    case WM_LBUTTONDBLCLK:
    {
        int xPos = GET_X_LPARAM(lParam);
        int yPos = GET_Y_LPARAM(lParam);

        // 检查是否双击在分割线区域
        if (abs(xPos - splitterPos) <= SPLITTER_WIDTH)
        {
            RECT rcClient;
            GetClientRect(hwnd, &rcClient);
            const int editHeight = 24;

            if (yPos >= editHeight)
            {
                // 重置分割线到窗口中央
                int oldPos = splitterPos;
                splitterPos = rcClient.right / 2;
                UpdateLayout(hwnd);

                // 保存新的分割线位置
                if (splitterPos != oldPos)
                {
                    SaveSplitterConfig();
                }

                if (hwndStatus)
                {
                    SendMessage(hwndStatus, SB_SETTEXT, 0, (LPARAM) "分割线已重置到中央位置");
                    SetTimer(hwnd, 1, 2000, NULL); // 2秒后清除消息
                }

                InvalidateRect(hwnd, NULL, TRUE);
            }
        }
        return 0;
    }

    case WM_LBUTTONUP:
    {
        if (isDragging)
        {
            isDragging = FALSE;
            ReleaseCapture();

            // 恢复线程优先级
            SetThreadPriority(GetCurrentThread(), THREAD_PRIORITY_NORMAL);

            // 根据鼠标位置设置光标
            POINT pt;
            GetCursorPos(&pt);
            ScreenToClient(hwnd, &pt);

            if (IsMouseOverSplitter(pt.x, pt.y))
            {
                SetCursor(LoadCursor(NULL, IDC_SIZEWE));
            }
            else
            {
                SetCursor(LoadCursor(NULL, IDC_ARROW));
            }

            // 最终更新布局
            UpdateLayout(hwnd);

            // 计算拖拽距离
            int dragDistance = abs(splitterPos - originalSplitterPos);

            // 如果分割线位置发生了变化，保存分割线配置
            if (dragDistance > 0)
            {
                SaveSplitterConfig();
            }

            // 更新状态栏消息
            if (hwndStatus)
            {
                if (dragDistance > 0)
                {
                    char statusText[100];
                    sprintf(statusText, "分割线调整完成 - 位置: %d (移动了 %d 像素)",
                            splitterPos, dragDistance);
                    SendMessage(hwndStatus, SB_SETTEXT, 0, (LPARAM)statusText);

                    // 2秒后清除消息
                    SetTimer(hwnd, 1, 2000, NULL);
                }
                else
                {
                    // 如果没有移动，显示当前位置和窗口信息
                    RECT rcClient;
                    GetClientRect(hwnd, &rcClient);
                    int maxSplitPos = rcClient.right - MIN_PANE_WIDTH;

                    char statusText[150];
                    sprintf(statusText, "分割线位置: %d (窗口宽度: %d, 最大位置: %d)",
                            splitterPos, rcClient.right, maxSplitPos);
                    SendMessage(hwndStatus, SB_SETTEXT, 0, (LPARAM)statusText);
                    SetTimer(hwnd, 1, 3000, NULL);
                }
            }

            // 强制重绘
            InvalidateRect(hwnd, NULL, TRUE);
            UpdateWindow(hwnd);
        }
        return 0;
    }

    case WM_TIMER:
    {
        if (wParam == 1) // 状态栏清除定时器（拖拽完成消息）
        {
            KillTimer(hwnd, 1);
            if (hwndStatus)
            {
                SendMessage(hwndStatus, SB_SETTEXT, 0, (LPARAM) "就绪");
            }
        }
        else if (wParam == 2) // 状态栏清除定时器（列操作消息）
        {
            KillTimer(hwnd, 2);
            if (hwndStatus)
            {
                SendMessage(hwndStatus, SB_SETTEXT, 0, (LPARAM) "就绪");
            }
        }
        else if (wParam == 5) // 状态栏清除定时器（配置加载消息）
        {
            KillTimer(hwnd, 5);
            if (hwndStatus)
            {
                SendMessage(hwndStatus, SB_SETTEXT, 0, (LPARAM) "就绪");
            }
        }

        return 0;
    }

    case WM_SIZING:
        SendMessage(hwndStatus, WM_SIZE, 0, 0);
        return TRUE;

    case WM_GETMINMAXINFO:
    {
        MINMAXINFO *mmi = (MINMAXINFO *)lParam;
        mmi->ptMinTrackSize.x = 400;
        mmi->ptMinTrackSize.y = 300;
        return 0;
    }

    case WM_PAINT:
    {
        PAINTSTRUCT ps;
        HDC hdc = BeginPaint(hwnd, &ps);
        RECT rcClient;
        GetClientRect(hwnd, &rcClient);

        // 创建用于双缓冲的内存 DC
        HDC memDC = CreateCompatibleDC(hdc);
        HBITMAP memBitmap = CreateCompatibleBitmap(hdc, rcClient.right, rcClient.bottom);
        HBITMAP oldBitmap = SelectObject(memDC, memBitmap);

        // 扁平风格背景 - 浅灰色
        FillRect(memDC, &rcClient, hBackBrush);

        // 绘制1px边框 - 扁平风格，0px间隙
        HPEN borderPen = CreatePen(PS_SOLID, 1, RGB(200, 200, 200));
        HPEN oldPen = SelectObject(memDC, borderPen);
        HBRUSH oldBrush = SelectObject(memDC, GetStockObject(NULL_BRUSH)); // 不填充

        // 筛选输入框1px边框 - 占满顶部宽度，0px间隙
        Rectangle(memDC, 0, 0, rcClient.right, 24);

        // 左侧窗口1px边框 - 从编辑框下方开始，0px间隙
        Rectangle(memDC, 0, 24, splitterPos - 1, rcClient.bottom - 25);

        // 右侧窗口1px边框 - 从分割线右侧开始，0px间隙
        Rectangle(memDC, splitterPos + 1, 24, rcClient.right, rcClient.bottom - 25);

        SelectObject(memDC, oldBrush);

        // 绘制扁平风格分割线
        HPEN splitPen = CreatePen(PS_SOLID, SPLIT_WIDTH, RGB(180, 180, 180));
        SelectObject(memDC, splitPen);
        MoveToEx(memDC, splitterPos, 24, NULL);           // 从编辑框下方开始
        LineTo(memDC, splitterPos, rcClient.bottom - 25); // 到状态栏上方结束

        SelectObject(memDC, oldPen);
        DeleteObject(borderPen);
        DeleteObject(splitPen);

        // 将内存 DC 的内容复制到窗口
        BitBlt(hdc, 0, 0, rcClient.right, rcClient.bottom, memDC, 0, 0, SRCCOPY);

        // 清理资源
        SelectObject(memDC, oldBitmap);
        DeleteObject(memBitmap);
        DeleteDC(memDC);

        EndPaint(hwnd, &ps);
        return 0;
    }

    case WM_ERASEBKGND:
    {
        return 1; // 返回1表示我们已处理背景，防止系统清除
    }

    case WM_CLOSE:
        // 在窗口关闭前保存配置
        if (hwndStatus)
        {
            SendMessage(hwndStatus, SB_SETTEXT, 0, (LPARAM) "正在保存配置...");
            UpdateWindow(hwnd);
        }

        SaveAllConfig(hwnd);

        if (hwndStatus)
        {
            SendMessage(hwndStatus, SB_SETTEXT, 0, (LPARAM) "配置已保存");
            UpdateWindow(hwnd);
            Sleep(500); // 短暂延迟以显示消息
        }

        DestroyWindow(hwnd);
        return 0;

    case WM_DESTROY:
        // 再次保存配置以确保保存成功
        SaveAllConfig(hwnd);

        if (hwndStatus)
        {
            DestroyWindow(hwndStatus);
        }
        PostQuitMessage(0);
        return 0;

    case WM_NOTIFY:
    {
        LPNMHDR pnmh = (LPNMHDR)lParam;

        if (pnmh->hwndFrom == hwndStatus)
        {
            return 0;
        }
        else if (pnmh->hwndFrom == hwndLeft)
        {
            switch (pnmh->code)
            {
            case NM_CUSTOMDRAW:
            {
                LPNMLVCUSTOMDRAW pLVCD = (LPNMLVCUSTOMDRAW)lParam;

                switch (pLVCD->nmcd.dwDrawStage)
                {
                case CDDS_PREPAINT:
                    return CDRF_NOTIFYITEMDRAW;

                case CDDS_ITEMPREPAINT:
                    // 设置行高20px的效果
                    pLVCD->nmcd.rc.bottom = pLVCD->nmcd.rc.top + 20;

                    // 设置文字颜色
                    pLVCD->clrText = RGB(51, 51, 51);
                    pLVCD->clrTextBk = RGB(255, 255, 255);

                    // 选中行的颜色
                    if (pLVCD->nmcd.uItemState & CDIS_SELECTED)
                    {
                        pLVCD->clrText = RGB(255, 255, 255);
                        pLVCD->clrTextBk = RGB(0, 120, 215);
                    }

                    return CDRF_NEWFONT;
                }
                break;
            }

            case LVN_ITEMCHANGED:
            {
                // 处理选择变化
                LPNMLISTVIEW pnmv = (LPNMLISTVIEW)lParam;
                if (pnmv->uNewState & LVIS_SELECTED)
                {
                    // 更新右侧详细信息
                    char itemText[256];
                    ListView_GetItemText(hwndLeft, pnmv->iItem, 0, itemText, sizeof(itemText));

                    char detailText[1024];
                    sprintf(detailText, "选中文件详细信息：\r\n\r\n文件名：%s\r\n\r\n这里可以显示：\r\n- 文件完整路径\r\n- 文件大小详情\r\n- 创建/修改时间\r\n- 文件属性\r\n- 预览内容", itemText);

                    SetWindowText(hwndRight, detailText);
                }
                break;
            }

            case LVN_COLUMNCLICK:
            {
                // 处理列头点击（可用于排序）
                LPNMLISTVIEW pnmv = (LPNMLISTVIEW)lParam;
                if (hwndStatus)
                {
                    char statusText[100];
                    const char *columnNames[] = {"Name", "Path", "Size", "Date Modified"};
                    sprintf(statusText, "点击了列: %s (可拖拽调整列顺序)",
                            columnNames[pnmv->iSubItem]);
                    SendMessage(hwndStatus, SB_SETTEXT, 0, (LPARAM)statusText);
                    SetTimer(hwnd, 2, 3000, NULL); // 3秒后清除消息
                }
                break;
            }

            case HDN_ENDDRAG:
            {
                // 处理列头拖拽结束
                if (hwndStatus)
                {
                    SendMessage(hwndStatus, SB_SETTEXT, 0, (LPARAM) "列顺序已调整");
                    SetTimer(hwnd, 2, 2000, NULL); // 2秒后清除消息
                }

                // 保存文件树配置
                SaveFileTreeConfig(hwndLeft);
                break;
            }

            case HDN_ENDTRACK:
            {
                // 处理列宽度调整结束
                if (hwndStatus)
                {
                    SendMessage(hwndStatus, SB_SETTEXT, 0, (LPARAM) "列宽度已调整");
                    SetTimer(hwnd, 2, 1500, NULL); // 1.5秒后清除消息
                }

                // 保存文件树配置
                SaveFileTreeConfig(hwndLeft);
                break;
            }
            }
        }
        break;
    }
    }
    return DefWindowProc(hwnd, uMsg, wParam, lParam);
}

int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow)
{
    // 加载通用控件库
    HMODULE hComCtl = LoadLibrary("comctl32.dll");
    if (!hComCtl)
    {
        MessageBox(NULL, "Failed to load comctl32.dll", "Error", MB_OK);
        return 1;
    }

    // 初始化通用控件
    INITCOMMONCONTROLSEX icex;
    icex.dwSize = sizeof(INITCOMMONCONTROLSEX);
    icex.dwICC = ICC_WIN95_CLASSES | ICC_BAR_CLASSES;
    if (!InitCommonControlsEx(&icex))
    {
        MessageBox(NULL, "Failed to initialize common controls", "Error", MB_OK);
        FreeLibrary(hComCtl);
        return 1;
    }

    // 注册窗口类
    static char szWindowClass[] = "FilePreviewerClass";
    WNDCLASSEX wcex = {0};
    wcex.cbSize = sizeof(WNDCLASSEX);
    wcex.lpfnWndProc = WindowProc;
    wcex.hInstance = hInstance;
    wcex.lpszClassName = szWindowClass;
    wcex.style = CS_HREDRAW | CS_VREDRAW | CS_DBLCLKS; // 添加双击支持
    wcex.hIcon = LoadIcon(hInstance, IDI_APPLICATION);
    wcex.hCursor = LoadCursor(NULL, IDC_ARROW);
    wcex.hbrBackground = NULL; // 不使用背景画刷
    wcex.hIconSm = LoadIcon(hInstance, IDI_APPLICATION);

    if (!RegisterClassEx(&wcex))
    {
        MessageBox(NULL, "Window Registration Failed!", "Error", MB_ICONEXCLAMATION | MB_OK);
        return 0;
    }

    // 创建主窗口
    HWND hwnd = CreateWindowEx(
        0,
        szWindowClass,
        "File Previewer",
        WS_OVERLAPPEDWINDOW | WS_CLIPCHILDREN,
        CW_USEDEFAULT, CW_USEDEFAULT, 800, 600,
        NULL,
        NULL,
        hInstance,
        NULL);

    if (hwnd == NULL)
    {
        MessageBox(NULL, "Window Creation Failed!", "Error", MB_OK);
        return 0;
    }

    // 阶段1: 只加载窗口配置
    LoadWindowConfig(hwnd);

    // 创建状态栏之前刷新窗口
    UpdateWindow(hwnd);
    ShowWindow(hwnd, nCmdShow);

    // 消息循环
    MSG msg = {0};
    while (GetMessage(&msg, NULL, 0, 0))
    {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }

    // 清理资源
    if (hBackBrush)
        DeleteObject(hBackBrush);
    if (hEditBrush)
        DeleteObject(hEditBrush);
    if (hSplitterBrush)
        DeleteObject(hSplitterBrush);
    if (hFlatBrush)
        DeleteObject(hFlatBrush);
    if (hFont)
        DeleteObject(hFont);
    if (hStatusFont)
        DeleteObject(hStatusFont);

    return 0;
}

// 配置文件相关函数实现

// 加载窗口配置
void LoadWindowConfig(HWND hwnd)
{
    isLoadingConfig = TRUE; // 设置加载标志

    // 检查配置文件是否存在
    DWORD fileAttr = GetFileAttributes(CONFIG_FILE);
    if (fileAttr == INVALID_FILE_ATTRIBUTES)
    {
        // 配置文件不存在，创建默认配置文件
        WritePrivateProfileString("Window", "X", "100", CONFIG_FILE);
        WritePrivateProfileString("Window", "Y", "100", CONFIG_FILE);
        WritePrivateProfileString("Window", "Width", "800", CONFIG_FILE);
        WritePrivateProfileString("Window", "Height", "600", CONFIG_FILE);
        WritePrivateProfileString("Window", "Maximized", "0", CONFIG_FILE);
        WritePrivateProfileString("Splitter", "Position", "300", CONFIG_FILE);
        WritePrivateProfileString("FileTree", "ColumnOrder", "0,1,2,3", CONFIG_FILE);
        WritePrivateProfileString("FileTree", "ColumnWidth0", "200", CONFIG_FILE);
        WritePrivateProfileString("FileTree", "ColumnWidth1", "300", CONFIG_FILE);
        WritePrivateProfileString("FileTree", "ColumnWidth2", "100", CONFIG_FILE);
        WritePrivateProfileString("FileTree", "ColumnWidth3", "150", CONFIG_FILE);
        WritePrivateProfileString(NULL, NULL, NULL, CONFIG_FILE); // 强制写入
        isLoadingConfig = FALSE;
        return; // 使用默认值
    }

    // 加载窗口位置和大小
    int x = GetPrivateProfileInt("Window", "X", 100, CONFIG_FILE);
    int y = GetPrivateProfileInt("Window", "Y", 100, CONFIG_FILE);
    int width = GetPrivateProfileInt("Window", "Width", 800, CONFIG_FILE);
    int height = GetPrivateProfileInt("Window", "Height", 600, CONFIG_FILE);
    int maximized = GetPrivateProfileInt("Window", "Maximized", 0, CONFIG_FILE);

    // 验证窗口位置和大小的有效性 - 支持4K等大屏幕和多显示器
    int screenWidth = GetSystemMetrics(SM_CXVIRTUALSCREEN);  // 虚拟屏幕宽度（所有显示器）
    int screenHeight = GetSystemMetrics(SM_CYVIRTUALSCREEN); // 虚拟屏幕高度（所有显示器）
    int screenLeft = GetSystemMetrics(SM_XVIRTUALSCREEN);    // 虚拟屏幕左边界
    int screenTop = GetSystemMetrics(SM_YVIRTUALSCREEN);     // 虚拟屏幕上边界

    // 如果虚拟屏幕信息无效，回退到主显示器
    if (screenWidth <= 0 || screenHeight <= 0)
    {
        screenWidth = GetSystemMetrics(SM_CXSCREEN);
        screenHeight = GetSystemMetrics(SM_CYSCREEN);
        screenLeft = 0;
        screenTop = 0;
    }

    // 验证位置 - 确保窗口至少部分可见（支持多显示器）
    if (x < screenLeft - width + 100 || x > screenLeft + screenWidth - 100)
        x = screenLeft + 100;
    if (y < screenTop || y > screenTop + screenHeight - 100)
        y = screenTop + 100;

    // 验证大小 - 支持4K等大屏幕，最大支持8K分辨率
    if (width < 400)
        width = 800;
    if (height < 300)
        height = 600;

    // 支持最大8K分辨率，但不超过实际屏幕尺寸
    int maxWidth = (screenWidth > 8192) ? 8192 : screenWidth;
    int maxHeight = (screenHeight > 8192) ? 8192 : screenHeight;

    if (width > maxWidth)
        width = maxWidth;
    if (height > maxHeight)
        height = maxHeight;

    // 如果调整后的窗口位置会导致窗口超出屏幕，重新调整位置
    if (x + width > screenLeft + screenWidth)
        x = screenLeft + screenWidth - width;
    if (y + height > screenTop + screenHeight)
        y = screenTop + screenHeight - height;

    // 确保位置不超出虚拟屏幕边界
    if (x < screenLeft)
        x = screenLeft;
    if (y < screenTop)
        y = screenTop;

    // 设置窗口位置和大小
    SetWindowPos(hwnd, NULL, x, y, width, height, SWP_NOZORDER);

    if (maximized)
    {
        ShowWindow(hwnd, SW_MAXIMIZE);
        wasMaximized = TRUE;
    }
    else
    {
        wasMaximized = FALSE;
    }

    isLoadingConfig = FALSE; // 清除加载标志
}

// 加载UI配置（分割线和文件树）
void LoadUIConfig(HWND hwnd)
{
    // 先更新布局确保窗口大小正确
    UpdateLayout(hwnd);

    // 加载分割线配置
    LoadSplitterConfig();

    // 验证并修复分割线位置
    ValidateAndFixSplitterPosition(hwnd);

    // 加载文件树配置
    LoadFileTreeConfig(hwndLeft);

    // 最终更新布局
    UpdateLayout(hwnd);
}

// 验证并修复分割线位置
void ValidateAndFixSplitterPosition(HWND hwnd)
{
    RECT rcClient;
    GetClientRect(hwnd, &rcClient);

    int minSplitPos = MIN_PANE_WIDTH;
    int maxSplitPos = rcClient.right - MIN_PANE_WIDTH;

    // 如果窗口太小，使用中央位置
    if (maxSplitPos <= minSplitPos)
    {
        splitterPos = rcClient.right / 2;
        return;
    }

    // 如果分割线位置超出范围，调整到有效范围内
    if (splitterPos < minSplitPos)
    {
        splitterPos = minSplitPos;
    }
    else if (splitterPos > maxSplitPos)
    {
        // 如果超出最大位置，尝试按比例缩放
        double ratio = (double)splitterPos / (double)(splitterPos + MIN_PANE_WIDTH);
        splitterPos = (int)(rcClient.right * ratio);

        // 确保仍在有效范围内
        if (splitterPos > maxSplitPos)
            splitterPos = maxSplitPos;
        if (splitterPos < minSplitPos)
            splitterPos = minSplitPos;
    }
}

// 验证并修复窗口位置
void ValidateAndFixWindowPosition(HWND hwnd)
{
    // 这个函数在LoadWindowConfig中已经实现了验证逻辑
    // 这里可以添加运行时的窗口位置验证
}

// 加载分割线配置
void LoadSplitterConfig()
{
    isLoadingConfig = TRUE; // 设置加载标志

    // 检查配置文件是否存在
    DWORD fileAttr = GetFileAttributes(CONFIG_FILE);
    if (fileAttr == INVALID_FILE_ATTRIBUTES)
    {
        splitterPos = 300; // 使用默认值
        configLoaded = TRUE;
        isLoadingConfig = FALSE;
        return;
    }

    // 加载分割线位置
    int loadedPos = GetPrivateProfileInt("Splitter", "Position", 300, CONFIG_FILE);

    // 基本有效性检查 - 支持4K等大分辨率，最大值设为8192
    if (loadedPos >= MIN_PANE_WIDTH && loadedPos <= 8192)
    {
        splitterPos = loadedPos;
    }
    else
    {
        splitterPos = 300; // 使用默认值
    }

    configLoaded = TRUE;     // 标记配置已加载
    isLoadingConfig = FALSE; // 清除加载标志
}

// 保存窗口配置
void SaveWindowConfig(HWND hwnd)
{
    WINDOWPLACEMENT wp;
    wp.length = sizeof(WINDOWPLACEMENT);
    if (!GetWindowPlacement(hwnd, &wp))
    {
        return; // 获取窗口位置失败
    }

    char buffer[32];

    // 获取要保存的窗口位置和大小
    RECT rect;
    BOOL isMaximized = (wp.showCmd == SW_MAXIMIZE || wp.showCmd == SW_SHOWMAXIMIZED);

    if (isMaximized)
    {
        // 使用正常状态的窗口位置和大小
        rect = wp.rcNormalPosition;
    }
    else
    {
        // 使用当前窗口位置和大小
        GetWindowRect(hwnd, &rect);
    }

    // 验证要保存的数据是否合理 - 支持4K等大分辨率
    int width = rect.right - rect.left;
    int height = rect.bottom - rect.top;

    // 支持最大8K分辨率 (7680x4320) 加上一些余量
    if (width < 100 || height < 100 || width > 8192 || height > 8192)
    {
        // 数据不合理，不保存
        return;
    }

    // 保存窗口位置和大小
    sprintf(buffer, "%d", rect.left);
    WritePrivateProfileString("Window", "X", buffer, CONFIG_FILE);

    sprintf(buffer, "%d", rect.top);
    WritePrivateProfileString("Window", "Y", buffer, CONFIG_FILE);

    sprintf(buffer, "%d", width);
    WritePrivateProfileString("Window", "Width", buffer, CONFIG_FILE);

    sprintf(buffer, "%d", height);
    WritePrivateProfileString("Window", "Height", buffer, CONFIG_FILE);

    sprintf(buffer, "%d", isMaximized ? 1 : 0);
    WritePrivateProfileString("Window", "Maximized", buffer, CONFIG_FILE);

    // 强制写入文件
    WritePrivateProfileString(NULL, NULL, NULL, CONFIG_FILE);
}

// 保存分割线配置
void SaveSplitterConfig()
{
    char buffer[32];
    sprintf(buffer, "%d", splitterPos);
    WritePrivateProfileString("Splitter", "Position", buffer, CONFIG_FILE);
    WritePrivateProfileString(NULL, NULL, NULL, CONFIG_FILE);
}

// 保存UI配置
void SaveUIConfig(HWND hwnd)
{
    SaveSplitterConfig();
    SaveFileTreeConfig(hwndLeft);
}

// 保存所有配置
void SaveAllConfig(HWND hwnd)
{
    SaveWindowConfig(hwnd);
    SaveUIConfig(hwnd);
}

// 加载文件树配置
void LoadFileTreeConfig(HWND hwndListView)
{
    if (!hwndListView || !IsWindow(hwndListView))
        return;

    // 检查配置文件是否存在
    DWORD fileAttr = GetFileAttributes(CONFIG_FILE);
    if (fileAttr == INVALID_FILE_ATTRIBUTES)
    {
        return; // 使用默认列宽度
    }

    char buffer[256];
    char keyName[32];

    // 加载列宽度
    for (int i = 0; i < 4; i++)
    {
        sprintf(keyName, "ColumnWidth%d", i);
        int defaultWidths[] = {200, 300, 100, 150};
        int width = GetPrivateProfileInt("FileTree", keyName, defaultWidths[i], CONFIG_FILE);

        // 验证列宽度的有效性
        if (width < 50)
            width = defaultWidths[i];
        if (width > 500)
            width = defaultWidths[i];

        ListView_SetColumnWidth(hwndListView, i, width);
    }

    // 加载列顺序
    GetPrivateProfileString("FileTree", "ColumnOrder", "0,1,2,3", buffer, sizeof(buffer), CONFIG_FILE);

    // 解析列顺序字符串并应用
    int columnOrder[4] = {0, 1, 2, 3}; // 默认顺序
    char *token = strtok(buffer, ",");
    int validOrder = 1; // 标记顺序是否有效

    for (int i = 0; i < 4 && token != NULL; i++)
    {
        int order = atoi(token);
        if (order >= 0 && order < 4) // 验证顺序的有效性
        {
            columnOrder[i] = order;
        }
        else
        {
            validOrder = 0; // 无效顺序
            break;
        }
        token = strtok(NULL, ",");
    }

    // 只有在顺序有效时才设置列顺序
    if (validOrder)
    {
        ListView_SetColumnOrderArray(hwndListView, 4, columnOrder);
    }
}

// 保存文件树配置
void SaveFileTreeConfig(HWND hwndListView)
{
    if (!hwndListView || !IsWindow(hwndListView))
        return;

    char buffer[256];
    char keyName[32];

    // 保存列宽度
    for (int i = 0; i < 4; i++)
    {
        sprintf(keyName, "ColumnWidth%d", i);
        int width = ListView_GetColumnWidth(hwndListView, i);
        if (width > 0) // 确保宽度有效
        {
            sprintf(buffer, "%d", width);
            WritePrivateProfileString("FileTree", keyName, buffer, CONFIG_FILE);
        }
    }

    // 保存列顺序
    int columnOrder[4];
    if (ListView_GetColumnOrderArray(hwndListView, 4, columnOrder))
    {
        sprintf(buffer, "%d,%d,%d,%d", columnOrder[0], columnOrder[1], columnOrder[2], columnOrder[3]);
        WritePrivateProfileString("FileTree", "ColumnOrder", buffer, CONFIG_FILE);
    }

    // 强制写入文件
    WritePrivateProfileString(NULL, NULL, NULL, CONFIG_FILE);
}
