#include <windows.h>
#include <commctrl.h>
#include <stdio.h>
#include <windowsx.h>
#include <string.h>
#include <locale.h>
#include "sqlite3.h"
#include "usn_journal.h"
#include "file_watcher.h"

#pragma comment(lib, "comctl32.lib")
#pragma comment(lib, "gdi32.lib")
#pragma comment(lib, "user32.lib")
#pragma comment(lib, "sqlite3.lib")

// Configuration constants
#define CONFIG_FILE ".\\config.ini"
#define MIN_PANE_WIDTH 100       // Minimum width for left/right panes
#define SPLITTER_WIDTH 1         // Visual width of splitter line
#define SPLITTER_DETECT_WIDTH 20 // Detection area: 20px on each side (40px total)
#define EDIT_HEIGHT 30           // Height of filter input box
#define FONT_SIZE 26             // Font size in points

// UI constants
#define TEXT_MARGIN 5         // Text margin in input box
#define TIMER_WINDOW_SAVE 2   // Timer ID for window save
#define TIMER_POSITION_SAVE 3 // Timer ID for position save
#define SAVE_DELAY_RESIZE 500 // Delay for resize save (ms)
#define SAVE_DELAY_MOVE 300   // Delay for move save (ms)

// Splitter settings

// Default window settings
#define DEFAULT_WINDOW_X 100
#define DEFAULT_WINDOW_Y 100
#define DEFAULT_WINDOW_WIDTH 1200
#define DEFAULT_WINDOW_HEIGHT 800
#define DEFAULT_SPLITTER_POS 400

// Splitter states
typedef enum
{
    SPLITTER_NORMAL,
    SPLITTER_HOVER,
    SPLITTER_DRAGGING
} SplitterState;

// Splitter management structure
typedef struct
{
    int position;
    int minPosition;
    int maxPosition;
    SplitterState state;
    BOOL isTracking;
    POINT lastMousePos;
    HCURSOR hCursorResize;
    HCURSOR hCursorNormal;
} SplitterManager;

// Global variables
static HWND hwndLeft, hwndRight, hwndEdit, hwndStatus;
static HFONT hFont;
static BOOL configLoaded = FALSE;
static BOOL windowInitialized = FALSE;
static BOOL wasMaximized = FALSE;
static BOOL g_fileTreeRenderInProgress = FALSE; // 控制文件树渲染状态
static HANDLE g_currentRenderThread = NULL;     // 当前渲染线程句柄

// Global splitter manager
static SplitterManager g_splitter;

// USN Journal and database variables
static HANDLE g_usnThread = NULL;
static BOOL g_indexingInProgress = FALSE;
sqlite3 *g_database = NULL; // Remove static to make it globally accessible

// Function declarations
LRESULT CALLBACK WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam);
void UpdateLayout(HWND hwnd);
void CreateControls(HWND hwnd);

// Configuration functions
void LoadConfig(HWND hwnd);
void SaveConfig(HWND hwnd);
void LoadColumnConfig();
void SaveColumnConfig();

// Splitter management functions
void InitializeSplitter(HWND hwnd);
void UpdateSplitterBounds(HWND hwnd);
BOOL IsSplitterHitTest(int x, int y);
void SetSplitterState(SplitterState newState);
void HandleSplitterMouseMove(HWND hwnd, int x, int y);
void HandleSplitterMouseDown(HWND hwnd, int x, int y);
void HandleSplitterMouseUp(HWND hwnd);
void DrawSplitter(HDC hdc, HWND hwnd);

// Database and USN Journal functions
void InitializeFileDatabase();
void StartUSNIndexing(HWND mainWindow);
void StartFullUSNScan(HWND mainWindow);
void StartFileTreeRendering(HWND mainWindow);
void StartFileTreeRenderingWithFilter(HWND mainWindow, const char *searchFilter);
void ApplyFileNameFilter(const char *filterText);        // 新的实时筛选函数
void PopulateListView(const char *searchFilter);         // ListView填充函数
char *stristr(const char *haystack, const char *needle); // 不区分大小写的字符串查找
void ClearListView();
DWORD WINAPI USNIndexingThread(LPVOID lpParam);
DWORD WINAPI FileTreeRenderThread(LPVOID lpParam);

// File tree rendering structure
typedef struct
{
    HWND hwndMainWindow;
    HWND hwndListView;
    HWND hwndStatus;
    char databasePath[MAX_PATH];
    char searchFilter[256];
    BOOL hasFilter;
} FileTreeRenderParams;

// 删除全局数组存储 - 改为直接操作ListView

// Update window layout
void UpdateLayout(HWND hwnd)
{
    RECT rcClient;
    GetClientRect(hwnd, &rcClient);

    // 状态栏高度
    int statusHeight = 0;
    if (hwndStatus)
    {
        RECT rcStatus;
        GetWindowRect(hwndStatus, &rcStatus);
        statusHeight = rcStatus.bottom - rcStatus.top;
        SendMessage(hwndStatus, WM_SIZE, 0, 0);
    }

    // 验证分割线位置
    int minPos = MIN_PANE_WIDTH;
    int maxPos = rcClient.right - MIN_PANE_WIDTH;
    if (g_splitter.position < minPos)
        g_splitter.position = minPos;
    if (g_splitter.position > maxPos)
        g_splitter.position = maxPos;

    // 布局控件
    const int editHeight = EDIT_HEIGHT;
    int availableHeight = rcClient.bottom - statusHeight;

    // 顶部编辑框
    MoveWindow(hwndEdit, 0, 0, rcClient.right, editHeight, TRUE);

    // 设置输入框的文本区域，确保垂直居中
    if (hwndEdit)
    {
        RECT rcEdit;
        GetClientRect(hwndEdit, &rcEdit);

        // 计算文本区域，使其在输入框中垂直居中
        int textHeight = FONT_SIZE; // 字体的实际高度
        int topMargin = (rcEdit.bottom - textHeight) / 2;

        RECT textRect;
        textRect.left = TEXT_MARGIN; // 左边距，光标靠左
        textRect.top = topMargin;
        textRect.right = rcEdit.right - TEXT_MARGIN; // 右边距
        textRect.bottom = topMargin + textHeight;

        SendMessage(hwndEdit, EM_SETRECT, 0, (LPARAM)&textRect);
    }

    // 左侧面板 (留出1px边框空间)
    MoveWindow(hwndLeft, 1, editHeight + 1, g_splitter.position - 1,
               availableHeight - editHeight - 2, TRUE);

    // 右侧面板 (留出1px边框空间)
    MoveWindow(hwndRight, g_splitter.position + SPLITTER_WIDTH + 1, editHeight + 1,
               rcClient.right - g_splitter.position - SPLITTER_WIDTH - 2,
               availableHeight - editHeight - 2, TRUE);

    // 触发重绘以显示边框和分割线
    if (windowInitialized)
    {
        // 重绘整个客户区以显示自绘边框
        InvalidateRect(hwnd, NULL, TRUE);
    }
}

// 创建控件
void CreateControls(HWND hwnd)
{
    // 初始化通用控件
    InitCommonControls();

    // 创建字体
    hFont = CreateFont(FONT_SIZE, 0, 0, 0, FW_NORMAL, FALSE, FALSE, FALSE,
                       DEFAULT_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS,
                       DEFAULT_QUALITY, DEFAULT_PITCH | FF_DONTCARE, "Microsoft YaHei");

    if (!hFont)
    {
        // Fallback to system font if custom font creation fails
        hFont = (HFONT)GetStockObject(DEFAULT_GUI_FONT);
    }

// 创建编辑框 (无默认文本，左对齐) - 添加ID以接收WM_COMMAND消息
#define ID_EDIT_FILTER 1001
    hwndEdit = CreateWindow("EDIT", "",
                            WS_CHILD | WS_VISIBLE | WS_BORDER | ES_LEFT,
                            0, 0, 0, 0, hwnd, (HMENU)ID_EDIT_FILTER, GetModuleHandle(NULL), NULL);

    // 创建左侧ListView (无边框，将自绘边框)
    hwndLeft = CreateWindow(WC_LISTVIEW, "",
                            WS_CHILD | WS_VISIBLE | LVS_REPORT | LVS_SINGLESEL,
                            0, 0, 0, 0, hwnd, NULL, GetModuleHandle(NULL), NULL);

    // Enable column reordering (only if ListView was created successfully)
    if (hwndLeft)
    {
        DWORD dwStyle = ListView_GetExtendedListViewStyle(hwndLeft);
        ListView_SetExtendedListViewStyle(hwndLeft, dwStyle | LVS_EX_HEADERDRAGDROP);
    }

    // 创建右侧显示窗口 (纯显示，无光标，无边框，将自绘边框)
    hwndRight = CreateWindow("STATIC", "",
                             WS_CHILD | WS_VISIBLE | SS_LEFT | SS_NOPREFIX,
                             0, 0, 0, 0, hwnd, NULL, GetModuleHandle(NULL), NULL);

    // 创建状态栏
    hwndStatus = CreateWindow(STATUSCLASSNAME, "",
                              WS_CHILD | WS_VISIBLE,
                              0, 0, 0, 0, hwnd, NULL, GetModuleHandle(NULL), NULL);

    // 设置字体
    if (hFont)
    {
        SendMessage(hwndEdit, WM_SETFONT, (WPARAM)hFont, TRUE);
        SendMessage(hwndLeft, WM_SETFONT, (WPARAM)hFont, TRUE);
        SendMessage(hwndRight, WM_SETFONT, (WPARAM)hFont, TRUE);
        SendMessage(hwndStatus, WM_SETFONT, (WPARAM)hFont, TRUE);
    }

    // 设置输入框内边距，使光标位置居中
    if (hwndEdit)
    {
        // 设置输入框边距
        SendMessage(hwndEdit, EM_SETMARGINS, EC_LEFTMARGIN | EC_RIGHTMARGIN,
                    MAKELPARAM(TEXT_MARGIN, TEXT_MARGIN));
    }

    // 设置ListView列
    LVCOLUMN lvc = {0};
    lvc.mask = LVCF_TEXT | LVCF_WIDTH;

    lvc.pszText = "Name";
    lvc.cx = 200;
    ListView_InsertColumn(hwndLeft, 0, &lvc);

    lvc.pszText = "Path";
    lvc.cx = 300;
    ListView_InsertColumn(hwndLeft, 1, &lvc);

    lvc.pszText = "Size";
    lvc.cx = 100;
    ListView_InsertColumn(hwndLeft, 2, &lvc);

    lvc.pszText = "Modified";
    lvc.cx = 150;
    ListView_InsertColumn(hwndLeft, 3, &lvc);

    InitializeSplitter(hwnd);

    // Load column configuration after creating ListView
    LoadColumnConfig();

    // Force check database and scan completion status
    BOOL dbExists = !IsFirstRun();
    BOOL scanCompleted = IsScanCompleted();

    printf("=== Startup Database Check ===\n");
    printf("Database file exists: %s\n", dbExists ? "YES" : "NO");
    printf("Scan completed flag: %s\n", scanCompleted ? "YES" : "NO");
    printf("==============================\n");

    // Force USN scan if database doesn't exist OR scan not completed
    if (!dbExists || !scanCompleted)
    {
        printf("FORCING FULL USN SCAN - Database missing or scan incomplete\n");

        // Initialize database and start USN indexing
        InitializeFileDatabase();
        StartUSNIndexing(hwnd);
    }
    else
    {
        printf("Database exists and scan completed - Starting file tree rendering...\n");

        // Initialize database connection
        InitializeFileDatabase();

        // Immediately start file tree rendering thread
        StartFileTreeRendering(hwnd);

        // Start background monitoring
        if (InitializeFileWatcher(hwnd))
        {
            if (StartFileWatching())
            {
                printf("Background USN monitoring started\n");
            }
        }
    }

    printf("Initialization completed successfully!\n");
}

// ========== SIMPLIFIED CONFIGURATION (like main.cpp) ==========

// 加载配置
void LoadConfig(HWND hwnd)
{
    // 检查配置文件
    DWORD fileAttr = GetFileAttributes(CONFIG_FILE);
    if (fileAttr == INVALID_FILE_ATTRIBUTES)
    {
        // 创建默认配置
        char buffer[32];
        sprintf(buffer, "%d", DEFAULT_WINDOW_X);
        WritePrivateProfileString("Window", "X", buffer, CONFIG_FILE);
        sprintf(buffer, "%d", DEFAULT_WINDOW_Y);
        WritePrivateProfileString("Window", "Y", buffer, CONFIG_FILE);
        sprintf(buffer, "%d", DEFAULT_WINDOW_WIDTH);
        WritePrivateProfileString("Window", "Width", buffer, CONFIG_FILE);
        sprintf(buffer, "%d", DEFAULT_WINDOW_HEIGHT);
        WritePrivateProfileString("Window", "Height", buffer, CONFIG_FILE);
        WritePrivateProfileString("Window", "Maximized", "0", CONFIG_FILE);
        sprintf(buffer, "%d", DEFAULT_SPLITTER_POS);
        WritePrivateProfileString("Splitter", "Position", buffer, CONFIG_FILE);
        WritePrivateProfileString(NULL, NULL, NULL, CONFIG_FILE);
        return;
    }

    // 加载窗口配置
    int x = GetPrivateProfileInt("Window", "X", DEFAULT_WINDOW_X, CONFIG_FILE);
    int y = GetPrivateProfileInt("Window", "Y", DEFAULT_WINDOW_Y, CONFIG_FILE);
    int width = GetPrivateProfileInt("Window", "Width", DEFAULT_WINDOW_WIDTH, CONFIG_FILE);
    int height = GetPrivateProfileInt("Window", "Height", DEFAULT_WINDOW_HEIGHT, CONFIG_FILE);
    int maximized = GetPrivateProfileInt("Window", "Maximized", 0, CONFIG_FILE);

    // 验证并设置窗口位置大小
    int screenWidth = GetSystemMetrics(SM_CXSCREEN);
    int screenHeight = GetSystemMetrics(SM_CYSCREEN);

    if (x < -100 || x > screenWidth - 200)
        x = DEFAULT_WINDOW_X;
    if (y < -100 || y > screenHeight - 200)
        y = DEFAULT_WINDOW_Y;
    if (width < 600 || width > screenWidth + 100)
        width = DEFAULT_WINDOW_WIDTH;
    if (height < 400 || height > screenHeight + 100)
        height = DEFAULT_WINDOW_HEIGHT;

    SetWindowPos(hwnd, NULL, x, y, width, height, SWP_NOZORDER);

    // Handle maximized state
    if (maximized)
    {
        ShowWindow(hwnd, SW_MAXIMIZE);
        wasMaximized = TRUE;
    }
    else
    {
        wasMaximized = FALSE;
    }

    // 加载分割线位置
    g_splitter.position = GetPrivateProfileInt("Splitter", "Position", DEFAULT_SPLITTER_POS, CONFIG_FILE);
    if (g_splitter.position < MIN_PANE_WIDTH)
        g_splitter.position = DEFAULT_SPLITTER_POS;

    configLoaded = TRUE;
}

// 保存配置
void SaveConfig(HWND hwnd)
{
    WINDOWPLACEMENT wp;
    wp.length = sizeof(WINDOWPLACEMENT);
    if (!GetWindowPlacement(hwnd, &wp))
        return;

    RECT rect;
    BOOL isMaximized = (wp.showCmd == SW_MAXIMIZE);

    // Always get the current actual window size (whether maximized or not)
    GetWindowRect(hwnd, &rect);

    char buffer[32];
    sprintf(buffer, "%d", rect.left);
    WritePrivateProfileString("Window", "X", buffer, CONFIG_FILE);

    sprintf(buffer, "%d", rect.top);
    WritePrivateProfileString("Window", "Y", buffer, CONFIG_FILE);

    sprintf(buffer, "%d", rect.right - rect.left);
    WritePrivateProfileString("Window", "Width", buffer, CONFIG_FILE);

    sprintf(buffer, "%d", rect.bottom - rect.top);
    WritePrivateProfileString("Window", "Height", buffer, CONFIG_FILE);

    // Save maximized state
    sprintf(buffer, "%d", isMaximized ? 1 : 0);
    WritePrivateProfileString("Window", "Maximized", buffer, CONFIG_FILE);

    sprintf(buffer, "%d", g_splitter.position);
    WritePrivateProfileString("Splitter", "Position", buffer, CONFIG_FILE);

    WritePrivateProfileString(NULL, NULL, NULL, CONFIG_FILE);
}

// Load column configuration
void LoadColumnConfig()
{
    if (!hwndLeft || !IsWindow(hwndLeft))
        return;

    // Load column order
    char columnOrder[256];
    GetPrivateProfileString("FileTree", "ColumnOrder", "0,1,2,3", columnOrder, sizeof(columnOrder), CONFIG_FILE);

    // Parse column order
    int order[4] = {0, 1, 2, 3};
    char *token = strtok(columnOrder, ",");
    int i = 0;
    while (token != NULL && i < 4)
    {
        order[i] = atoi(token);
        token = strtok(NULL, ",");
        i++;
    }

    // Set column order
    ListView_SetColumnOrderArray(hwndLeft, 4, order);

    // Load column widths
    for (int j = 0; j < 4; j++)
    {
        char keyName[32];
        sprintf(keyName, "ColumnWidth%d", j);
        int width = GetPrivateProfileInt("FileTree", keyName, 150, CONFIG_FILE);
        if (width > 0)
        {
            ListView_SetColumnWidth(hwndLeft, j, width);
        }
    }
}

// Save column configuration
void SaveColumnConfig()
{
    if (!hwndLeft || !IsWindow(hwndLeft))
        return;

    char buffer[256];

    // Save column widths
    for (int i = 0; i < 4; i++)
    {
        char keyName[32];
        sprintf(keyName, "ColumnWidth%d", i);
        int width = ListView_GetColumnWidth(hwndLeft, i);
        if (width > 0)
        {
            sprintf(buffer, "%d", width);
            WritePrivateProfileString("FileTree", keyName, buffer, CONFIG_FILE);
        }
    }

    // Save column order
    int columnOrder[4];
    if (ListView_GetColumnOrderArray(hwndLeft, 4, columnOrder))
    {
        sprintf(buffer, "%d,%d,%d,%d", columnOrder[0], columnOrder[1], columnOrder[2], columnOrder[3]);
        WritePrivateProfileString("FileTree", "ColumnOrder", buffer, CONFIG_FILE);
    }

    // Force write to file
    WritePrivateProfileString(NULL, NULL, NULL, CONFIG_FILE);
}

// ========== SPLITTER MANAGEMENT FUNCTIONS ==========

// Initialize splitter manager
void InitializeSplitter(HWND hwnd)
{
    g_splitter.position = DEFAULT_SPLITTER_POS;
    g_splitter.state = SPLITTER_NORMAL;
    g_splitter.isTracking = FALSE;
    g_splitter.lastMousePos.x = 0;
    g_splitter.lastMousePos.y = 0;

    // Load cursors
    g_splitter.hCursorResize = LoadCursor(NULL, IDC_SIZEWE);
    g_splitter.hCursorNormal = LoadCursor(NULL, IDC_ARROW);

    // Update bounds
    UpdateSplitterBounds(hwnd);
}

// Update splitter position bounds
void UpdateSplitterBounds(HWND hwnd)
{
    RECT rcClient;
    GetClientRect(hwnd, &rcClient);

    g_splitter.minPosition = MIN_PANE_WIDTH;
    g_splitter.maxPosition = rcClient.right - MIN_PANE_WIDTH;

    // Clamp current position to bounds
    if (g_splitter.position < g_splitter.minPosition)
        g_splitter.position = g_splitter.minPosition;
    if (g_splitter.position > g_splitter.maxPosition)
        g_splitter.position = g_splitter.maxPosition;
}

// Test if point is over splitter (enhanced detection)
BOOL IsSplitterHitTest(int x, int y)
{
    // Check if we're in the main content area (below edit box and above status bar)
    if (y < EDIT_HEIGHT)
        return FALSE;

    // Get window dimensions to check status bar area
    extern HWND hwndStatus;
    if (hwndStatus)
    {
        RECT rcStatus;
        GetWindowRect(hwndStatus, &rcStatus);
        RECT rcClient;
        GetClientRect(GetParent(hwndStatus), &rcClient);
        int statusHeight = rcStatus.bottom - rcStatus.top;

        // Don't detect in status bar area
        if (y > rcClient.bottom - statusHeight)
            return FALSE;
    }

    // Check X coordinate with enhanced detection zone
    int distance = abs(x - g_splitter.position);
    if (distance <= SPLITTER_DETECT_WIDTH)
        return TRUE;

    return FALSE;
}

// Set splitter state and update cursor
void SetSplitterState(SplitterState newState)
{
    if (g_splitter.state != newState)
    {
        g_splitter.state = newState;

        switch (newState)
        {
        case SPLITTER_NORMAL:
            SetCursor(g_splitter.hCursorNormal);
            break;

        case SPLITTER_HOVER:
        case SPLITTER_DRAGGING:
            SetCursor(g_splitter.hCursorResize);
            break;
        }
    }
}

// Handle mouse move over splitter
void HandleSplitterMouseMove(HWND hwnd, int x, int y)
{
    if (g_splitter.state == SPLITTER_DRAGGING)
    {
        // Update position while dragging
        int newPos = x;

        // Clamp to bounds
        if (newPos < g_splitter.minPosition)
            newPos = g_splitter.minPosition;
        if (newPos > g_splitter.maxPosition)
            newPos = g_splitter.maxPosition;

        if (newPos != g_splitter.position)
        {
            g_splitter.position = newPos;
            UpdateLayout(hwnd);

            // Enhanced real-time feedback during dragging
            if (hwndStatus)
            {
                char statusText[200];
                RECT rcClient;
                GetClientRect(hwnd, &rcClient);
                int leftWidth = g_splitter.position;
                int rightWidth = rcClient.right - g_splitter.position - SPLITTER_WIDTH;
                double leftPercent = (double)leftWidth / rcClient.right * 100.0;
                double rightPercent = (double)rightWidth / rcClient.right * 100.0;

                sprintf(statusText, "Dragging: %d px | Left: %d px (%.1f%%) | Right: %d px (%.1f%%)",
                        g_splitter.position, leftWidth, leftPercent, rightWidth, rightPercent);
                SendMessage(hwndStatus, SB_SETTEXT, 0, (LPARAM)statusText);
            }
        }

        SetSplitterState(SPLITTER_DRAGGING);
    }
    else if (IsSplitterHitTest(x, y))
    {
        // Mouse is over splitter - change to resize cursor
        SetSplitterState(SPLITTER_HOVER);

        if (hwndStatus)
        {
            char statusText[200];
            RECT rcClient;
            GetClientRect(hwnd, &rcClient);
            int leftWidth = g_splitter.position;
            int rightWidth = rcClient.right - g_splitter.position - SPLITTER_WIDTH;
            sprintf(statusText, "Resize splitter: %d px | Left: %d px | Right: %d px | Detection zone: %d px",
                    g_splitter.position, leftWidth, rightWidth, SPLITTER_DETECT_WIDTH * 2);
            SendMessage(hwndStatus, SB_SETTEXT, 0, (LPARAM)statusText);
        }
    }
    else
    {
        // Mouse is not over splitter - restore normal cursor
        if (g_splitter.state != SPLITTER_DRAGGING)
        {
            SetSplitterState(SPLITTER_NORMAL);

            if (hwndStatus)
            {
                SendMessage(hwndStatus, SB_SETTEXT, 0, (LPARAM) "Ready");
            }
        }
    }

    g_splitter.lastMousePos.x = x;
    g_splitter.lastMousePos.y = y;
}

// Handle left mouse down on splitter
void HandleSplitterMouseDown(HWND hwnd, int x, int y)
{
    if (IsSplitterHitTest(x, y))
    {
        g_splitter.isTracking = TRUE;
        SetSplitterState(SPLITTER_DRAGGING);
        SetCapture(hwnd);

        if (hwndStatus)
        {
            char statusText[200];
            RECT rcClient;
            GetClientRect(hwnd, &rcClient);
            sprintf(statusText, "Started dragging from %d px - Move mouse to resize (Range: %d-%d px)",
                    g_splitter.position, g_splitter.minPosition, g_splitter.maxPosition);
            SendMessage(hwndStatus, SB_SETTEXT, 0, (LPARAM)statusText);
        }
    }
}

// Handle mouse up on splitter
void HandleSplitterMouseUp(HWND hwnd)
{
    if (g_splitter.isTracking)
    {
        g_splitter.isTracking = FALSE;
        SetSplitterState(SPLITTER_NORMAL);
        ReleaseCapture();

        // Save configuration
        SaveConfig(hwnd);

        if (hwndStatus)
        {
            char statusText[150];
            RECT rcClient;
            GetClientRect(hwnd, &rcClient);
            sprintf(statusText, "Splitter saved at %d px (Left: %d, Right: %d)",
                    g_splitter.position, g_splitter.position,
                    rcClient.right - g_splitter.position - SPLITTER_WIDTH);
            SendMessage(hwndStatus, SB_SETTEXT, 0, (LPARAM)statusText);
        }
    }
}

// Draw splitter and borders
void DrawSplitter(HDC hdc, HWND hwnd)
{
    RECT rcClient;
    GetClientRect(hwnd, &rcClient);

    int statusHeight = 0;
    if (hwndStatus)
    {
        RECT rcStatus;
        GetWindowRect(hwndStatus, &rcStatus);
        statusHeight = rcStatus.bottom - rcStatus.top;
    }

    int availableHeight = rcClient.bottom - statusHeight;

    // Draw left panel border
    HPEN hBorderPen = CreatePen(PS_SOLID, 1, GetSysColor(COLOR_3DSHADOW));
    HPEN hOldPen = SelectObject(hdc, hBorderPen);

    RECT leftRect = {0, EDIT_HEIGHT, g_splitter.position, availableHeight};
    Rectangle(hdc, leftRect.left, leftRect.top, leftRect.right, leftRect.bottom);

    // Draw right panel border
    RECT rightRect = {g_splitter.position + SPLITTER_WIDTH, EDIT_HEIGHT, rcClient.right, availableHeight};
    Rectangle(hdc, rightRect.left, rightRect.top, rightRect.right, rightRect.bottom);

    SelectObject(hdc, hOldPen);
    DeleteObject(hBorderPen);

    // Draw simple splitter line (fixed color)
    HPEN hSplitterPen = CreatePen(PS_SOLID, SPLITTER_WIDTH, GetSysColor(COLOR_3DSHADOW));
    hOldPen = SelectObject(hdc, hSplitterPen);

    // Draw the splitter line
    MoveToEx(hdc, g_splitter.position, EDIT_HEIGHT, NULL);
    LineTo(hdc, g_splitter.position, availableHeight);

    // Clean up
    SelectObject(hdc, hOldPen);
    DeleteObject(hSplitterPen);
}

// ========== DATABASE AND USN JOURNAL FUNCTIONS ==========

// Initialize file database
void InitializeFileDatabase()
{
    printf("Initializing database...\n");

    if (InitializeDatabase("file_index.db") == SQLITE_OK)
    {
        // Database is already opened in InitializeDatabase function
        // Just get the handle
        int rc = sqlite3_open("file_index.db", &g_database);
        if (rc == SQLITE_OK)
        {
            printf("Database opened successfully\n");
            if (hwndStatus)
            {
                SendMessage(hwndStatus, SB_SETTEXT, 0, (LPARAM) "Database initialized successfully");
            }
        }
        else
        {
            printf("Failed to open database: %s\n", sqlite3_errmsg(g_database));
            if (hwndStatus)
            {
                SendMessage(hwndStatus, SB_SETTEXT, 0, (LPARAM) "Failed to open database");
            }
        }
    }
    else
    {
        printf("Failed to initialize database\n");
        if (hwndStatus)
        {
            SendMessage(hwndStatus, SB_SETTEXT, 0, (LPARAM) "Failed to initialize database");
        }
    }
}

// Start USN indexing - FORCE FULL SCAN
void StartUSNIndexing(HWND mainWindow)
{
    printf("=== FORCING FULL USN SCAN ===\n");
    printf("This function is called when database is missing or scan incomplete\n");
    printf("Starting comprehensive USN Journal scan of all NTFS drives...\n");
    printf("========================================\n");

    if (hwndStatus)
    {
        SendMessage(hwndStatus, SB_SETTEXT, 0, (LPARAM) "FORCE SCAN: Starting full disk scan - Please wait...");
    }

    // Always execute full USN scan when this function is called
    StartFullUSNScan(mainWindow);
}

// Start file tree rendering from existing database
void StartFileTreeRendering(HWND mainWindow)
{
    StartFileTreeRenderingWithFilter(mainWindow, NULL);
}

// Start file tree rendering with real-time filtering
void StartFileTreeRenderingWithFilter(HWND mainWindow, const char *searchFilter)
{
    printf("=== Starting File Tree Rendering with Real-time Filtering ===\n");
    if (searchFilter && strlen(searchFilter) > 0)
    {
        printf("Filter: '%s'\n", searchFilter);
    }
    else
    {
        printf("No filter - loading all data\n");
    }

    // 检查是否已有渲染在进行中
    if (g_fileTreeRenderInProgress)
    {
        printf("File tree rendering already in progress - skipping\n");
        return;
    }

    // Check if database file exists
    DWORD fileAttr = GetFileAttributes("file_index.db");
    if (fileAttr == INVALID_FILE_ATTRIBUTES)
    {
        printf("ERROR: Database file not found for file tree rendering\n");
        if (hwndStatus)
        {
            SendMessage(hwndStatus, SB_SETTEXT, 0, (LPARAM) "Database file not found");
        }
        return;
    }

    // Create parameters for file tree render thread
    FileTreeRenderParams *params = malloc(sizeof(FileTreeRenderParams));
    if (!params)
    {
        printf("ERROR: Failed to allocate memory for file tree render parameters\n");
        return;
    }

    params->hwndMainWindow = mainWindow;
    params->hwndListView = hwndLeft;
    params->hwndStatus = hwndStatus;
    strcpy(params->databasePath, "file_index.db");

    // 设置筛选参数
    params->hasFilter = (searchFilter && strlen(searchFilter) > 0);
    if (params->hasFilter)
    {
        strncpy(params->searchFilter, searchFilter, sizeof(params->searchFilter) - 1);
        params->searchFilter[sizeof(params->searchFilter) - 1] = '\0';
    }
    else
    {
        params->searchFilter[0] = '\0';
    }

    // 设置渲染状态
    g_fileTreeRenderInProgress = TRUE;

    // Create file tree render thread
    g_currentRenderThread = CreateThread(NULL, 0, FileTreeRenderThread, params, 0, NULL);
    if (g_currentRenderThread)
    {
        printf("File tree render thread created successfully\n");
        if (params->hasFilter)
        {
            printf("Real-time filtering active: '%s'\n", params->searchFilter);
        }
        else
        {
            printf("Loading all data without filter\n");
        }
    }
    else
    {
        printf("ERROR: Failed to create file tree render thread\n");
        g_fileTreeRenderInProgress = FALSE;
        free(params);
    }
}

// Start full USN scan for first run
void StartFullUSNScan(HWND mainWindow)
{
    printf("=== Starting Full USN Scan ===\n");

    // 首次运行，执行完整的USN扫描
    if (g_indexingInProgress)
    {
        if (hwndStatus)
        {
            SendMessage(hwndStatus, SB_SETTEXT, 0, (LPARAM) "Initial indexing already in progress");
        }
        return;
    }

    g_indexingInProgress = TRUE;
    g_usnThread = CreateThread(NULL, 0, USNIndexingThread, (LPVOID)mainWindow, 0, NULL);

    if (g_usnThread)
    {
        if (hwndStatus)
        {
            SendMessage(hwndStatus, SB_SETTEXT, 0, (LPARAM) "First run: Starting full file indexing...");
        }
    }
    else
    {
        g_indexingInProgress = FALSE;
        if (hwndStatus)
        {
            SendMessage(hwndStatus, SB_SETTEXT, 0, (LPARAM) "Failed to start indexing thread");
        }
    }
}

// USN indexing thread function
DWORD WINAPI USNIndexingThread(LPVOID lpParam)
{
    HWND mainWindow = (HWND)lpParam;

    printf("=== USN Journal Full Disk Scan Thread Started ===\n");
    printf("This is the FIRST RUN - scanning all NTFS volumes...\n");
    printf("Please wait while we build the complete file index...\n");

    // Update status bar
    if (hwndStatus)
    {
        SendMessage(hwndStatus, SB_SETTEXT, 0, (LPARAM) "First run: Scanning all disks - Please wait...");
    }

    // Process all volumes with detailed logging
    BOOL success = ProcessAllVolumes();

    if (success)
    {
        printf("=== USN Journal Full Scan COMPLETED Successfully ===\n");
        printf("All NTFS disks have been scanned and indexed to database.\n");
        printf("Database file 'file_index.db' now contains the complete file index.\n");
        printf("Next time the program starts, it will use background monitoring only.\n");

        // Update status bar
        if (hwndStatus)
        {
            SendMessage(hwndStatus, SB_SETTEXT, 0, (LPARAM) "Full scan completed - Preparing background monitoring...");
        }

        // Notify main thread that indexing is complete
        // The main thread will mark scan as complete and start monitoring
        PostMessage(mainWindow, WM_USER + 100, 0, 0);
    }
    else
    {
        printf("=== USN Journal Scan FAILED ===\n");
        printf("Error occurred during disk scanning.\n");
        printf("Please check:\n");
        printf("1. Running as Administrator\n");
        printf("2. NTFS drives are accessible\n");
        printf("3. Sufficient disk space for database\n");

        // Update status bar
        if (hwndStatus)
        {
            SendMessage(hwndStatus, SB_SETTEXT, 0, (LPARAM) "Full scan failed - Check permissions and try again");
        }

        PostMessage(mainWindow, WM_USER + 101, 0, 0);
    }

    g_indexingInProgress = FALSE;
    return 0;
}

// Clear ListView
void ClearListView()
{
    if (hwndLeft)
    {
        ListView_DeleteAllItems(hwndLeft);
    }
}

// Case-insensitive string search function
char *stristr(const char *haystack, const char *needle)
{
    if (!haystack || !needle)
        return NULL;
    if (*needle == '\0')
        return (char *)haystack;

    size_t needle_len = strlen(needle);
    for (const char *p = haystack; *p; p++)
    {
// 使用_strnicmp (Windows) 或 strncasecmp (Linux)
#ifdef _WIN32
        if (_strnicmp(p, needle, needle_len) == 0)
#else
        if (strncasecmp(p, needle, needle_len) == 0)
#endif
        {
            return (char *)p;
        }
    }
    return NULL;
}

// 数据缓存结构 - 用于实时筛选
typedef struct
{
    char name[MAX_PATH]; // 文件名（包括扩展名）
    char path[MAX_PATH]; // 完整路径
    char size[64];       // 格式化的文件大小
    char modified[64];   // 修改时间
} FileDataCache;

// 全局缓存变量
static FileDataCache *g_fileCache = NULL;
static int g_totalCachedFiles = 0;
static BOOL g_cacheReady = FALSE;

// 筛选相关
#define FILTER_TIMER_ID 100
static char g_lastFilter[256] = "";

// 基于缓存数据的实时文件名筛选
void ApplyFileNameFilter(const char *filterText)
{
    if (!hwndLeft)
    {
        printf("ERROR: ListView not available for filtering\n");
        return;
    }

    if (!g_cacheReady || !g_fileCache)
    {
        printf("ERROR: No cached data available for filtering\n");
        printf("  g_cacheReady: %s\n", g_cacheReady ? "TRUE" : "FALSE");
        printf("  g_fileCache: %p\n", g_fileCache);
        printf("  g_totalCachedFiles: %d\n", g_totalCachedFiles);
        return;
    }

    BOOL hasFilter = (filterText && strlen(filterText) > 0);
    printf("=== REAL-TIME FILTER START ===\n");
    printf("Filter text: '%s'\n", hasFilter ? filterText : "(clear)");
    printf("Total cached files: %d\n", g_totalCachedFiles);
    printf("Has filter: %s\n", hasFilter ? "YES" : "NO");

    // 特殊调试：如果筛选PDF，显示前几个文件名
    if (hasFilter && (stristr("pdf", filterText) || stristr("PDF", filterText)))
    {
        printf("=== PDF FILTER DEBUG ===\n");
        printf("Looking for PDF files in cache...\n");
        int pdfCount = 0;
        for (int debug_i = 0; debug_i < g_totalCachedFiles && debug_i < 20; debug_i++)
        {
            if (stristr(g_fileCache[debug_i].name, "pdf") || stristr(g_fileCache[debug_i].name, "PDF"))
            {
                printf("  Found PDF %d: '%s'\n", ++pdfCount, g_fileCache[debug_i].name);
            }
        }
        printf("Total PDF files found in first 20 items: %d\n", pdfCount);
        printf("=== END PDF DEBUG ===\n");
    }

    // 暂停重绘以提高性能
    SendMessage(hwndLeft, WM_SETREDRAW, FALSE, 0);

    // 清空ListView
    ListView_DeleteAllItems(hwndLeft);

    // 重新添加匹配的项目
    int visibleCount = 0;
    int checkedCount = 0;

    for (int i = 0; i < g_totalCachedFiles; i++)
    {
        BOOL shouldShow = TRUE;
        checkedCount++;

        if (hasFilter)
        {
            // 检查文件名是否包含筛选文本（不区分大小写）
            char *matchResult = stristr(g_fileCache[i].name, filterText);
            if (matchResult == NULL)
            {
                shouldShow = FALSE;
            }
            else
            {
                // 调试：显示前3个匹配项
                if (visibleCount < 3)
                {
                    printf("  MATCH %d: '%s' contains '%s'\n", visibleCount + 1, g_fileCache[i].name, filterText);
                }
            }
        }

        if (shouldShow)
        {
            // 添加匹配的项目 - 使用Unicode版本
            WCHAR nameW[MAX_PATH], pathW[MAX_PATH_LENGTH], sizeW[64], modifiedW[64];

            // 转换为宽字符
            MultiByteToWideChar(CP_UTF8, 0, g_fileCache[i].name, -1, nameW, MAX_PATH);
            MultiByteToWideChar(CP_UTF8, 0, g_fileCache[i].path, -1, pathW, MAX_PATH_LENGTH);
            MultiByteToWideChar(CP_UTF8, 0, g_fileCache[i].size, -1, sizeW, 64);
            MultiByteToWideChar(CP_UTF8, 0, g_fileCache[i].modified, -1, modifiedW, 64);

            LVITEMW lvi = {0};
            lvi.mask = LVIF_TEXT;
            lvi.iItem = visibleCount;
            lvi.iSubItem = 0;
            lvi.pszText = nameW;
            SendMessageW(hwndLeft, LVM_INSERTITEMW, 0, (LPARAM)&lvi);

            // 设置其他列
            SendMessageW(hwndLeft, LVM_SETITEMTEXTW, visibleCount,
                         (LPARAM) & (LVITEMW){.iSubItem = 1, .pszText = pathW});
            SendMessageW(hwndLeft, LVM_SETITEMTEXTW, visibleCount,
                         (LPARAM) & (LVITEMW){.iSubItem = 2, .pszText = sizeW});
            SendMessageW(hwndLeft, LVM_SETITEMTEXTW, visibleCount,
                         (LPARAM) & (LVITEMW){.iSubItem = 3, .pszText = modifiedW});

            visibleCount++;
        }
    }

    // 恢复重绘
    SendMessage(hwndLeft, WM_SETREDRAW, TRUE, 0);
    InvalidateRect(hwndLeft, NULL, TRUE);

    printf("=== FILTER RESULTS ===\n");
    printf("Checked: %d files\n", checkedCount);
    printf("Visible: %d files\n", visibleCount);
    printf("Hidden: %d files\n", checkedCount - visibleCount);
    printf("=== REAL-TIME FILTER END ===\n");

    // 更新状态栏
    if (hwndStatus)
    {
        char statusText[150];
        if (hasFilter)
        {
            snprintf(statusText, sizeof(statusText), "Filter '%s': %d files shown (of %d total)",
                     filterText, visibleCount, g_totalCachedFiles);
        }
        else
        {
            snprintf(statusText, sizeof(statusText), "All files: %d items", visibleCount);
        }
        SendMessage(hwndStatus, SB_SETTEXT, 0, (LPARAM)statusText);
    }
}

// PopulateListView function - 简化版本，只负责加载数据
void PopulateListView(const char *searchFilter)
{
    printf("PopulateListView called\n");

    // 检查数据库是否存在
    DWORD fileAttr = GetFileAttributes("file_index.db");
    if (fileAttr == INVALID_FILE_ATTRIBUTES)
    {
        printf("No database file found - cannot populate file tree\n");
        if (hwndStatus)
        {
            SendMessage(hwndStatus, SB_SETTEXT, 0, (LPARAM) "No database file - run full scan first");
        }
        return;
    }

    // 启动数据加载（加载所有数据，筛选在加载完成后进行）
    StartFileTreeRenderingWithFilter(GetParent(hwndLeft), searchFilter);
}

// Removed old ListView render thread - using FileTreeRenderThread only

// File Tree Render Thread - reads all USN data and renders to file tree
DWORD WINAPI FileTreeRenderThread(LPVOID lpParam)
{
    FileTreeRenderParams *params = (FileTreeRenderParams *)lpParam;
    if (!params)
    {
        printf("ERROR: Invalid file tree render parameters\n");
        return 1;
    }

    printf("=== File Tree Render Thread Started ===\n");
    printf("Architecture: Reading ALL USN data from database...\n");
    printf("Database: %s\n", params->databasePath);

    // Update status
    if (params->hwndStatus)
    {
        SendMessage(params->hwndStatus, SB_SETTEXT, 0, (LPARAM) "Reading USN database - Loading file tree...");
    }

    // Open database connection for this thread
    sqlite3 *db;
    int rc = sqlite3_open(params->databasePath, &db);
    if (rc != SQLITE_OK)
    {
        printf("ERROR: Cannot open USN database: %s\n", sqlite3_errmsg(db));
        if (params->hwndStatus)
        {
            SendMessage(params->hwndStatus, SB_SETTEXT, 0, (LPARAM) "Failed to open USN database");
        }
        free(params);
        return 1;
    }

    printf("USN database opened successfully\n");

    // Clear file tree first
    SendMessage(params->hwndListView, LVM_DELETEALLITEMS, 0, 0);
    printf("File tree cleared, starting to load USN data...\n");

    // Count total USN records
    sqlite3_stmt *countStmt;
    const char *countSql = "SELECT COUNT(*) FROM files";
    int totalRecords = 0;

    if (sqlite3_prepare_v2(db, countSql, -1, &countStmt, NULL) == SQLITE_OK)
    {
        if (sqlite3_step(countStmt) == SQLITE_ROW)
        {
            totalRecords = sqlite3_column_int(countStmt, 0);
            printf("Total USN records in database: %d\n", totalRecords);
        }
        sqlite3_finalize(countStmt);
    }

    if (totalRecords == 0)
    {
        printf("WARNING: No USN records found in database\n");
        if (params->hwndStatus)
        {
            SendMessage(params->hwndStatus, SB_SETTEXT, 0, (LPARAM) "No USN records found in database");
        }
        sqlite3_close(db);
        free(params);
        return 1;
    }

    // Prepare query to read ALL USN data (no database filtering)
    const char *sql = "SELECT name, path, size, modified FROM files ORDER BY path, name";
    printf("Loading ALL USN data - filtering will be done via ListView show/hide\n");

    sqlite3_stmt *stmt;
    if (sqlite3_prepare_v2(db, sql, -1, &stmt, NULL) != SQLITE_OK)
    {
        printf("ERROR: Failed to prepare USN query: %s\n", sqlite3_errmsg(db));
        if (params->hwndStatus)
        {
            SendMessage(params->hwndStatus, SB_SETTEXT, 0, (LPARAM) "Failed to prepare USN query");
        }
        sqlite3_close(db);
        free(params);
        return 1;
    }

    // 分配缓存内存
    if (g_fileCache)
    {
        free(g_fileCache);
        g_fileCache = NULL;
    }

    g_fileCache = malloc(totalRecords * sizeof(FileDataCache));
    if (!g_fileCache)
    {
        printf("ERROR: Failed to allocate cache memory\n");
        sqlite3_finalize(stmt);
        sqlite3_close(db);
        free(params);
        return 1;
    }

    g_totalCachedFiles = 0;
    g_cacheReady = FALSE;

    printf("Starting to load USN data to cache and render to file tree...\n");

    // Render all USN data to file tree
    int itemIndex = 0;
    int processedCount = 0;

    while (sqlite3_step(stmt) == SQLITE_ROW)
    {
        // Extract USN Journal data from database
        const char *name = (const char *)sqlite3_column_text(stmt, 0);     // USN Record FileName
        const char *path = (const char *)sqlite3_column_text(stmt, 1);     // FRN rebuilt path
        LONGLONG size = sqlite3_column_int64(stmt, 2);                     // File size
        const char *modified = (const char *)sqlite3_column_text(stmt, 3); // Modification time

        if (!name || !path || g_totalCachedFiles >= totalRecords)
            continue; // Skip invalid records or overflow

        // 缓存所有数据（不管是否匹配筛选）
        strncpy(g_fileCache[g_totalCachedFiles].name, name, sizeof(g_fileCache[g_totalCachedFiles].name) - 1);
        g_fileCache[g_totalCachedFiles].name[sizeof(g_fileCache[g_totalCachedFiles].name) - 1] = '\0';

        strncpy(g_fileCache[g_totalCachedFiles].path, path, sizeof(g_fileCache[g_totalCachedFiles].path) - 1);
        g_fileCache[g_totalCachedFiles].path[sizeof(g_fileCache[g_totalCachedFiles].path) - 1] = '\0';

        // 检查是否需要应用筛选（只影响显示，不影响缓存）
        BOOL shouldShow = TRUE;
        if (params->hasFilter && strlen(params->searchFilter) > 0)
        {
            // 检查文件名（包括后缀）是否包含筛选文本（不区分大小写）
            if (stristr(name, params->searchFilter) == NULL)
            {
                shouldShow = FALSE;
            }
        }

        // Format size string and cache it
        if (size > 0)
        {
            if (size >= 1024 * 1024 * 1024)
                snprintf(g_fileCache[g_totalCachedFiles].size, sizeof(g_fileCache[g_totalCachedFiles].size),
                         "%.1f GB", size / (1024.0 * 1024.0 * 1024.0));
            else if (size >= 1024 * 1024)
                snprintf(g_fileCache[g_totalCachedFiles].size, sizeof(g_fileCache[g_totalCachedFiles].size),
                         "%.1f MB", size / (1024.0 * 1024.0));
            else if (size >= 1024)
                snprintf(g_fileCache[g_totalCachedFiles].size, sizeof(g_fileCache[g_totalCachedFiles].size),
                         "%.1f KB", size / 1024.0);
            else
                snprintf(g_fileCache[g_totalCachedFiles].size, sizeof(g_fileCache[g_totalCachedFiles].size),
                         "%lld bytes", size);
        }
        else
        {
            strcpy(g_fileCache[g_totalCachedFiles].size, "Directory");
        }

        // Cache modified time
        strncpy(g_fileCache[g_totalCachedFiles].modified, modified ? modified : "",
                sizeof(g_fileCache[g_totalCachedFiles].modified) - 1);
        g_fileCache[g_totalCachedFiles].modified[sizeof(g_fileCache[g_totalCachedFiles].modified) - 1] = '\0';

        // 只有匹配筛选的项目才添加到ListView显示
        if (shouldShow)
        {
            // 添加到ListView - 使用Unicode版本
            WCHAR nameW[MAX_PATH], pathW[MAX_PATH_LENGTH], sizeW[64], modifiedW[64];

            // 转换为宽字符
            MultiByteToWideChar(CP_UTF8, 0, g_fileCache[g_totalCachedFiles].name, -1, nameW, MAX_PATH);
            MultiByteToWideChar(CP_UTF8, 0, g_fileCache[g_totalCachedFiles].path, -1, pathW, MAX_PATH_LENGTH);
            MultiByteToWideChar(CP_UTF8, 0, g_fileCache[g_totalCachedFiles].size, -1, sizeW, 64);
            MultiByteToWideChar(CP_UTF8, 0, g_fileCache[g_totalCachedFiles].modified, -1, modifiedW, 64);

            LVITEMW lvi = {0};
            lvi.mask = LVIF_TEXT;
            lvi.iItem = itemIndex;
            lvi.iSubItem = 0;
            lvi.pszText = nameW;
            SendMessageW(params->hwndListView, LVM_INSERTITEMW, 0, (LPARAM)&lvi);

            // Set other columns
            SendMessageW(params->hwndListView, LVM_SETITEMTEXTW, itemIndex,
                         (LPARAM) & (LVITEMW){.iSubItem = 1, .pszText = pathW});
            SendMessageW(params->hwndListView, LVM_SETITEMTEXTW, itemIndex,
                         (LPARAM) & (LVITEMW){.iSubItem = 2, .pszText = sizeW});
            SendMessageW(params->hwndListView, LVM_SETITEMTEXTW, itemIndex,
                         (LPARAM) & (LVITEMW){.iSubItem = 3, .pszText = modifiedW});

            itemIndex++;
        }

        // 增加缓存计数（无论是否显示）
        g_totalCachedFiles++;
        processedCount++;

        // 减少进度更新频率，避免频繁刷新
        if (processedCount % 10000 == 0)
        {
            printf("Processed %d USN records...\n", processedCount);
        }
    }

    // 设置缓存完成标志
    g_cacheReady = TRUE;

    sqlite3_finalize(stmt);
    sqlite3_close(db);

    // Final status update
    printf("File tree rendering completed: %d USN records loaded and ready for filtering\n", itemIndex);
    if (params->hwndStatus)
    {
        char statusText[120];
        snprintf(statusText, sizeof(statusText), "File tree loaded: %d files ready for filtering", itemIndex);
        SendMessage(params->hwndStatus, SB_SETTEXT, 0, (LPARAM)statusText);
    }

    // 重置渲染状态
    g_fileTreeRenderInProgress = FALSE;
    if (g_currentRenderThread)
    {
        CloseHandle(g_currentRenderThread);
        g_currentRenderThread = NULL;
    }

    printf("=== File Tree Render Thread Completed ===\n");
    printf("File tree is ready for use - waiting for filter operations\n");
    free(params);
    return 0;
}

// 窗口过程
LRESULT CALLBACK WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam)
{
    switch (uMsg)
    {
    case WM_CREATE:
        CreateControls(hwnd);
        UpdateLayout(hwnd);
        SetFocus(hwndEdit);
        windowInitialized = TRUE; // Mark window as fully initialized

        // 自动加载数据到缓存以支持实时筛选
        printf("Checking for existing database to load data...\n");
        DWORD fileAttr = GetFileAttributes("file_index.db");
        if (fileAttr != INVALID_FILE_ATTRIBUTES)
        {
            printf("Database found - loading data for real-time filtering\n");
            PopulateListView(NULL); // 加载所有数据到缓存
        }
        else
        {
            printf("No database found - please run full scan first\n");
            if (hwndStatus)
            {
                SendMessage(hwndStatus, SB_SETTEXT, 0, (LPARAM) "No database - run full scan first");
            }
        }
        return 0;

    case WM_PAINT:
    {
        PAINTSTRUCT ps;
        HDC hdc = BeginPaint(hwnd, &ps);

        // 绘制边框和分割线
        if (windowInitialized)
        {
            DrawSplitter(hdc, hwnd);
        }

        EndPaint(hwnd, &ps);
        return 0;
    }

    case WM_SIZE:
    {
        UpdateLayout(hwnd);

        // Update splitter bounds when window size changes
        if (windowInitialized)
        {
            UpdateSplitterBounds(hwnd);
        }

        // Check if window state changed
        BOOL isMaximized = (wParam == SIZE_MAXIMIZED);
        BOOL isRestored = (wParam == SIZE_RESTORED);

        if (windowInitialized)
        {
            // Handle maximization state change
            if (isMaximized && !wasMaximized)
            {
                // Window was just maximized
                RECT rcClient;
                GetClientRect(hwnd, &rcClient);

                if (hwndStatus)
                {
                    char statusText[150];
                    sprintf(statusText, "Window maximized - Size: %dx%d",
                            rcClient.right, rcClient.bottom);
                    SendMessage(hwndStatus, SB_SETTEXT, 0, (LPARAM)statusText);
                }

                // Save configuration immediately
                SaveConfig(hwnd);
                wasMaximized = TRUE;
            }
            else if (isRestored && wasMaximized)
            {
                // Window was restored from maximized
                if (hwndStatus)
                {
                    SendMessage(hwndStatus, SB_SETTEXT, 0, (LPARAM) "Window restored from maximized");
                }

                // Save configuration immediately
                SaveConfig(hwnd);
                wasMaximized = FALSE;
            }
            else if (wParam != SIZE_MINIMIZED)
            {
                // Normal resize, use timer to avoid frequent saves
                SetTimer(hwnd, TIMER_WINDOW_SAVE, SAVE_DELAY_RESIZE, NULL);
            }
        }
        return 0;
    }

    case WM_MOVE:
        // Save window position immediately if window is initialized
        if (windowInitialized)
        {
            // Use a timer to avoid saving too frequently during move
            SetTimer(hwnd, TIMER_POSITION_SAVE, SAVE_DELAY_MOVE, NULL);
        }
        return 0;

    case WM_LBUTTONDOWN:
    {
        // 检查是否点击了右侧显示窗口
        POINT pt = {GET_X_LPARAM(lParam), GET_Y_LPARAM(lParam)};
        HWND hwndClicked = ChildWindowFromPoint(hwnd, pt);

        if (hwndClicked == hwndRight)
        {
            // 点击右侧窗口时，将焦点设置回筛选输入框
            SetFocus(hwndEdit);
            return 0;
        }

        // 继续处理分割线拖拽
        int xPos = GET_X_LPARAM(lParam);
        int yPos = GET_Y_LPARAM(lParam);

        HandleSplitterMouseDown(hwnd, xPos, yPos);
        return 0;
    }

    case WM_SETFOCUS:
        // 确保焦点始终在筛选输入框
        if (hwndEdit && IsWindow(hwndEdit))
        {
            SetFocus(hwndEdit);
        }
        return 0;

    case WM_MOUSEMOVE:
    {
        int xPos = GET_X_LPARAM(lParam);
        int yPos = GET_Y_LPARAM(lParam);

        HandleSplitterMouseMove(hwnd, xPos, yPos);
        return 0;
    }

    case WM_SETCURSOR:
    {
        // Enhanced cursor handling for splitter
        if (LOWORD(lParam) == HTCLIENT)
        {
            POINT pt;
            GetCursorPos(&pt);
            ScreenToClient(hwnd, &pt);

            if (IsSplitterHitTest(pt.x, pt.y))
            {
                // Force resize cursor when over splitter
                SetCursor(g_splitter.hCursorResize);
                return TRUE;
            }
            else
            {
                // Force normal cursor when not over splitter
                SetCursor(g_splitter.hCursorNormal);
                return TRUE;
            }
        }
        break; // Let default handler process other cases
    }

    case WM_LBUTTONUP:
        HandleSplitterMouseUp(hwnd);
        return 0;

        // Right-click functionality removed - only left-click drag is supported

    case WM_COMMAND:
    {
        // Handle edit box text changes for search
        printf("WM_COMMAND received: LOWORD=%d, HIWORD=%d\n",
               LOWORD(wParam), HIWORD(wParam));

        if (LOWORD(wParam) == ID_EDIT_FILTER && HIWORD(wParam) == EN_CHANGE)
        {
            char searchText[256];
            GetWindowTextA(hwndEdit, searchText, sizeof(searchText));

            printf("EN_CHANGE detected! Search text: '%s'\n", searchText);

            // 实时文件名筛选
            SetTimer(hwnd, FILTER_TIMER_ID, 50, NULL); // 50ms延迟，平衡响应速度和性能
            printf("Filter timer set for 50ms\n");
        }
        break;
    }

    case WM_TIMER:
    {
        switch (wParam)
        {
        case FILTER_TIMER_ID: // Real-time filename filter timer
            KillTimer(hwnd, FILTER_TIMER_ID);
            {
                char searchText[256];
                GetWindowTextA(hwndEdit, searchText, sizeof(searchText));

                // 使用新的实时筛选架构
                printf("Timer triggered - search text: '%s'\n", searchText);

                if (strlen(searchText) > 0)
                {
                    printf("Applying real-time filename filter: '%s'\n", searchText);
                    ApplyFileNameFilter(searchText);
                }
                else
                {
                    printf("Clearing filename filter - showing all files\n");
                    ApplyFileNameFilter(NULL);
                }
            }
            break;

        case TIMER_WINDOW_SAVE: // Window size change timer
            KillTimer(hwnd, TIMER_WINDOW_SAVE);
            SaveConfig(hwnd);
            if (hwndStatus)
            {
                SendMessage(hwndStatus, SB_SETTEXT, 0, (LPARAM) "Window configuration saved");
            }
            break;

        case TIMER_POSITION_SAVE: // Window position change timer
            KillTimer(hwnd, TIMER_POSITION_SAVE);
            SaveConfig(hwnd);
            if (hwndStatus)
            {
                SendMessage(hwndStatus, SB_SETTEXT, 0, (LPARAM) "Window position saved");
            }
            break;
        }
        return 0;
    }

    case WM_USER + 100: // USN indexing completed successfully
    {
        g_indexingInProgress = FALSE;

        printf("=== Post-Scan Setup ===\n");
        printf("Marking scan as completed...\n");

        // 标记初始扫描完成
        MarkInitialScanComplete();

        printf("Starting background USN monitoring...\n");

        if (hwndStatus)
        {
            SendMessage(hwndStatus, SB_SETTEXT, 0, (LPARAM) "Scan completed - Starting background monitoring...");
        }

        // 启动文件监控
        if (InitializeFileWatcher(hwnd))
        {
            if (StartFileWatching())
            {
                if (hwndStatus)
                {
                    SendMessage(hwndStatus, SB_SETTEXT, 0, (LPARAM) "Background monitoring active - System ready");
                }
                printf("Background USN monitoring started successfully\n");
            }
            else
            {
                printf("WARNING: Failed to start background monitoring\n");
            }
        }
        else
        {
            printf("WARNING: Failed to initialize file watcher\n");
        }

        // Start file tree rendering with new architecture
        printf("Starting file tree rendering with new architecture...\n");
        StartFileTreeRendering(hwnd);

        printf("=== System Ready ===\n");
        printf("Full scan completed, file tree rendering started, and background monitoring active.\n");
        printf("The system is now ready for use.\n");

        return 0;
    }

    case WM_USER + 101: // USN indexing failed
    {
        g_indexingInProgress = FALSE;

        printf("=== USN Scan Failed ===\n");
        printf("Scan failed - InitialScanCompleted will NOT be set to 1\n");
        printf("Next program start will retry the scan\n");

        if (hwndStatus)
        {
            SendMessage(hwndStatus, SB_SETTEXT, 0, (LPARAM) "File indexing failed - Will retry on next start");
        }

        // Do NOT call MarkInitialScanComplete() here
        // This ensures the scan will be retried next time

        return 0;
    }

    // 文件监控消息处理
    case WM_FILE_CHANGE_DETECTED:
    {
        // 文件变化检测到
        static int changeCount = 0;
        changeCount++;

        if (hwndStatus)
        {
            char statusText[100];
            const char *changeType = "";

            switch (wParam)
            {
            case CHANGE_TYPE_ADDED:
                changeType = "added";
                break;
            case CHANGE_TYPE_REMOVED:
                changeType = "removed";
                break;
            case CHANGE_TYPE_MODIFIED:
                changeType = "modified";
                break;
            case CHANGE_TYPE_RENAMED:
                changeType = "renamed";
                break;
            default:
                changeType = "changed";
                break;
            }

            snprintf(statusText, sizeof(statusText), "File %s (%d changes) - Database updated", changeType, changeCount);
            SendMessage(hwndStatus, SB_SETTEXT, 0, (LPARAM)statusText);
        }

        // 禁用自动刷新 - 文件树等待筛选操作
        printf("File change detected - database updated (auto-refresh disabled)\n");

        // 不自动刷新文件树，等待用户筛选
        // 用户可以通过输入筛选条件来手动刷新
        return 0;
    }

    case WM_USER + 2:
        // USN Journal变化通知 - 禁用自动刷新
        printf("Received USN Journal change notification - auto-refresh disabled\n");

        // 只更新状态栏，不自动刷新文件树
        if (hwndStatus)
        {
            SendMessage(hwndStatus, SB_SETTEXT, 0, (LPARAM) "USN changes detected - use filter to refresh");
        }
        return 0;

    case WM_NOTIFY:
    {
        LPNMHDR pnmh = (LPNMHDR)lParam;

        // Check if notification is from ListView or its header
        HWND hwndHeader = ListView_GetHeader(hwndLeft);
        if (pnmh->hwndFrom == hwndLeft || pnmh->hwndFrom == hwndHeader)
        {
            switch (pnmh->code)
            {
            case HDN_BEGINDRAG:
                // Allow column dragging
                if (hwndStatus)
                {
                    SendMessage(hwndStatus, SB_SETTEXT, 0, (LPARAM) "Dragging column...");
                }
                return FALSE;

            case HDN_ENDDRAG:
            {
                // Column order changed, save configuration immediately
                // Use PostMessage to delay saving until after the drag operation completes
                PostMessage(hwnd, WM_USER + 1, 0, 0);
                if (hwndStatus)
                {
                    SendMessage(hwndStatus, SB_SETTEXT, 0, (LPARAM) "Column order changed");
                }
                return FALSE;
            }

            case HDN_ENDTRACK:
            {
                // Column width changed, save configuration immediately
                SaveColumnConfig();
                if (hwndStatus)
                {
                    SendMessage(hwndStatus, SB_SETTEXT, 0, (LPARAM) "Column width saved");
                }
                return FALSE;
            }

            case HDN_ITEMCHANGED:
            {
                // Column properties changed
                SaveColumnConfig();
                return FALSE;
            }
            }
        }
        break;
    }

    case WM_USER + 1:
        // Delayed save for column order changes
        SaveColumnConfig();
        return 0;

        // WM_TIMER handling moved to earlier location

    case WM_CLOSE:
        SaveConfig(hwnd);
        SaveColumnConfig();
        DestroyWindow(hwnd);
        return 0;

    case WM_DESTROY:
        printf("WM_DESTROY received - cleaning up...\n");

        // 停止文件监控
        CleanupFileWatcher();

        // Close database
        if (g_database)
        {
            printf("Closing database...\n");
            sqlite3_close(g_database);
            g_database = NULL;
        }

        // Wait for USN thread to complete
        if (g_usnThread)
        {
            printf("Waiting for USN thread to complete...\n");
            WaitForSingleObject(g_usnThread, 5000);
            CloseHandle(g_usnThread);
            g_usnThread = NULL;
        }

        // 停止文件树渲染线程
        if (g_fileTreeRenderInProgress && g_currentRenderThread)
        {
            printf("Stopping file tree render thread...\n");
            TerminateThread(g_currentRenderThread, 0);
            CloseHandle(g_currentRenderThread);
            g_currentRenderThread = NULL;
            g_fileTreeRenderInProgress = FALSE;
        }

        if (hFont)
            DeleteObject(hFont);

        printf("Posting quit message...\n");
        PostQuitMessage(0);
        return 0;
    }

    return DefWindowProc(hwnd, uMsg, wParam, lParam);
}

// 主函数
int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow)
{
    // 设置控制台和程序支持UTF-8
    SetConsoleOutputCP(CP_UTF8);
    SetConsoleCP(CP_UTF8);

    // 设置程序的默认字符编码为UTF-8
    setlocale(LC_ALL, ".UTF8");

    const char *szWindowClass = "FilePreviewerSimple";

    // 注册窗口类
    WNDCLASS wc = {0};
    wc.lpfnWndProc = WindowProc;
    wc.hInstance = hInstance;
    wc.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
    wc.lpszClassName = szWindowClass;
    wc.hCursor = LoadCursor(NULL, IDC_ARROW);

    if (!RegisterClass(&wc))
    {
        MessageBox(NULL, "Window Registration Failed!", "Error", MB_OK);
        return 0;
    }

    // 创建窗口
    HWND hwnd = CreateWindow(szWindowClass, "File Previewer - Simple",
                             WS_OVERLAPPEDWINDOW | WS_CLIPCHILDREN,
                             CW_USEDEFAULT, CW_USEDEFAULT,
                             DEFAULT_WINDOW_WIDTH, DEFAULT_WINDOW_HEIGHT,
                             NULL, NULL, hInstance, NULL);

    if (!hwnd)
    {
        MessageBox(NULL, "Window Creation Failed!", "Error", MB_OK);
        return 0;
    }

    // 检查运行状态并显示信息
    BOOL isDbFirstRun = IsFirstRun();
    BOOL isConfigFirstRun = IsConfigFirstRun();

    printf("=== File Previewer Startup ===\n");
    printf("Database (file_index.db): %s\n", isDbFirstRun ? "First run - will create and scan all files" : "Exists - will monitor changes only");
    printf("Config (config.ini): %s\n", isConfigFirstRun ? "First run - will create defaults" : "Exists - will load settings");

    if (isDbFirstRun)
    {
        printf("\nNOTE: First run will scan entire disk using USN Journal\n");
        printf("This may take several minutes depending on disk size...\n");
    }
    printf("===============================\n");

    // Load configuration (simplified like main.cpp)
    LoadConfig(hwnd);

    // 显示窗口
    ShowWindow(hwnd, nCmdShow);
    UpdateWindow(hwnd);

    // 消息循环
    MSG msg;
    while (GetMessage(&msg, NULL, 0, 0))
    {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }

    return (int)msg.wParam;
}
