#include <windows.h>

// 定义图标资源ID
#define IDI_MAIN_ICON 101

// 主程序图标
IDI_MAIN_ICON ICON "resource/icons8_64.ico"

// 版本信息资源
VS_VERSION_INFO VERSIONINFO
FILEVERSION 1,0,0,1
PRODUCTVERSION 1,0,0,1
FILEFLAGSMASK VS_FFI_FILEFLAGSMASK
FILEFLAGS 0x0L
FILEOS VOS_NT_WINDOWS32
FILETYPE VFT_APP
FILESUBTYPE VFT2_UNKNOWN
BEGIN
    BLOCK "StringFileInfo"
    BEGIN
        BLOCK "040904B0"
        BEGIN
            VALUE "CompanyName", "<EMAIL>"
            VALUE "FileDescription", "EveryView - High Performance File Previewer"
            VALUE "FileVersion", "*******"
            VALUE "InternalName", "EveryView"
            VALUE "LegalCopyright", "Copyright (C) 2025 <EMAIL>"
            VALUE "OriginalFilename", "EveryView.exe"
            VALUE "ProductName", "EveryView"
            VALUE "ProductVersion", "*******"
            VALUE "Comments", "High performance file browser and previewer based on USN Journal"
        END
    END
    BLOCK "VarFileInfo"
    BEGIN
        VALUE "Translation", 0x409, 1200
    END
END
