#include <windows.h>

// 定义图标资源ID
#define IDI_MAIN_ICON 101

// 主程序图标
IDI_MAIN_ICON ICON "resource/icons8_64.ico"

// 版本信息资源
1 VERSIONINFO
FILEVERSION 1,0,0,1
PRODUCTVERSION 1,0,0,1
FILEFLAGSMASK VS_FFI_FILEFLAGSMASK
FILEFLAGS 0x0L
FILEOS VOS_NT_WINDOWS32
FILETYPE VFT_APP
FILESUBTYPE VFT2_UNKNOWN
BEGIN
    BLOCK "StringFileInfo"
    BEGIN
        BLOCK "040904E4"
        BEGIN
            VALUE "CompanyName", "<EMAIL>\0"
            VALUE "FileDescription", "EveryView - 高性能文件预览器\0"
            VALUE "FileVersion", "*******\0"
            VALUE "InternalName", "EveryView\0"
            VALUE "LegalCopyright", "Copyright (C) 2025 <EMAIL>\0"
            VALUE "OriginalFilename", "EveryView.exe\0"
            VALUE "ProductName", "EveryView\0"
            VALUE "ProductVersion", "*******\0"
            VALUE "Comments", "基于USN Journal的高性能文件浏览和预览工具\0"
        END
    END
    BLOCK "VarFileInfo"
    BEGIN
        VALUE "Translation", 0x0409, 0x04E4
    END
END
