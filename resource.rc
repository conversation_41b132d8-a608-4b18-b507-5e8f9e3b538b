// 定义图标资源ID
#define IDI_MAIN_ICON 101

// 主程序图标 - 使用32x32像素的图标作为主图标
IDI_MAIN_ICON ICON "resource/icons8_32.ico"

// 版本信息
VS_VERSION_INFO VERSIONINFO
FILEVERSION 1,0,0,0
PRODUCTVERSION 1,0,0,0
FILEFLAGSMASK 0x3fL
FILEFLAGS 0x0L
FILEOS 0x40004L
FILETYPE 0x1L
FILESUBTYPE 0x0L
BEGIN
    BLOCK "StringFileInfo"
    BEGIN
        BLOCK "040904b0"
        BEGIN
            VALUE "CompanyName", "Everyview64"
            VALUE "FileDescription", "Everyview64 - 文件预览器"
            VALUE "FileVersion", "*******"
            VALUE "InternalName", "Everyview64"
            VALUE "LegalCopyright", "Copyright (C) 2024"
            VALUE "OriginalFilename", "main.exe"
            VALUE "ProductName", "Everyview64"
            VALUE "ProductVersion", "*******"
        END
    END
    BLOCK "VarFileInfo"
    BEGIN
        VALUE "Translation", 0x409, 1200
    END
END
