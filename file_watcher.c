#include "file_watcher.h"

// ============================================================================
// 时间转换工具函数（被usn_journal.c调用）
// ============================================================================

// 转换文件时间为字符串
void ConvertFileTimeToString(const FILETIME *fileTime, char *timeString, size_t bufferSize)
{
    SYSTEMTIME st;
    if (FileTimeToSystemTime(fileTime, &st))
    {
        snprintf(timeString, bufferSize, "%04d/%02d/%02d-%02d:%02d",
                 st.wYear, st.wMonth, st.wDay, st.wHour, st.wMinute);
    }
    else
    {
        strncpy(timeString, "Unknown", bufferSize - 1);
        timeString[bufferSize - 1] = '\0';
    }
}

// ============================================================================
// 未使用的函数（占位符）
// ============================================================================

BOOL IsNTFSDrive(WCHAR driveLetter) { return FALSE; }
BOOL GetFileInfo(const WCHAR *filePath, LARGE_INTEGER *fileSize, FILETIME *lastWriteTime, BOOL *isDirectory) { return FALSE; }
void FormatFileSize(LARGE_INTEGER fileSize, char *sizeStr, size_t bufferSize) {}
BOOL InitializeFileWatcher(HWND hwnd) { return FALSE; }
BOOL StartFileWatching() { return FALSE; }
BOOL StartDriveWatcher(DriveWatcher *watcher) { return FALSE; }
DWORD WINAPI DriveWatcherThread(LPVOID lpParam) { return 0; }
void ProcessDirectoryChanges(DriveWatcher *watcher, BYTE *buffer, DWORD bufferSize) {}
void ProcessFileChangeEvent(const FileChangeEvent *event) {}
BOOL AddFileToIndex(const WCHAR *fileName, const WCHAR *fullPath, const LARGE_INTEGER *fileSize, const FILETIME *lastWriteTime) { return TRUE; }
BOOL RemoveFileFromIndex(const WCHAR *fullPath) { return TRUE; }
BOOL UpdateFileInIndex(const WCHAR *fullPath, const LARGE_INTEGER *fileSize, const FILETIME *lastWriteTime) { return TRUE; }
BOOL IsFirstRun() { return FALSE; }
BOOL IsConfigFirstRun() { return FALSE; }
void MarkInitialScanComplete() {}
BOOL IsScanCompleted() { return FALSE; }
BOOL ShouldDoInitialScan() { return FALSE; }
BOOL StopFileWatching() { return TRUE; }
BOOL StopDriveWatcher(DriveWatcher *watcher) { return TRUE; }
void CleanupFileWatcher() {}
