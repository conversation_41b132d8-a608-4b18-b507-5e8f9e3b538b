#include "file_watcher.h"

// ============================================================================
// 全局变量和常量定义
// ============================================================================
static FileWatchManager g_watchManager = {0};

// ============================================================================
// 辅助函数
// ============================================================================

// 检查是否为NTFS驱动器
BOOL IsNTFSDrive(WCHAR driveLetter)
{
    WCHAR rootPath[4] = {driveLetter, L':', L'\\', L'\0'};
    WCHAR fileSystem[32];

    if (GetVolumeInformationW(rootPath, NULL, 0, NULL, NULL, NULL,
                              fileSystem, sizeof(fileSystem) / sizeof(WCHAR)))
    {
        return wcscmp(fileSystem, L"NTFS") == 0;
    }
    return FALSE;
}

// 获取文件信息
BOOL GetFileInfo(const WCHAR *filePath, LARGE_INTEGER *fileSize,
                 FILETIME *lastWriteTime, BOOL *isDirectory)
{
    WIN32_FIND_DATAW findData;
    HANDLE hFind = FindFirstFileW(filePath, &findData);

    if (hFind == INVALID_HANDLE_VALUE)
        return FALSE;

    FindClose(hFind);

    if (fileSize)
    {
        fileSize->LowPart = findData.nFileSizeLow;
        fileSize->HighPart = findData.nFileSizeHigh;
    }

    if (lastWriteTime)
        *lastWriteTime = findData.ftLastWriteTime;

    if (isDirectory)
        *isDirectory = (findData.dwFileAttributes & FILE_ATTRIBUTE_DIRECTORY) != 0;

    return TRUE;
}

// 转换文件时间为字符串
void ConvertFileTimeToString(const FILETIME *fileTime, char *timeString, size_t bufferSize)
{
    SYSTEMTIME st;
    if (FileTimeToSystemTime(fileTime, &st))
    {
        snprintf(timeString, bufferSize, "%04d/%02d/%02d-%02d:%02d",
                 st.wYear, st.wMonth, st.wDay, st.wHour, st.wMinute);
    }
    else
    {
        strncpy(timeString, "Unknown", bufferSize - 1);
        timeString[bufferSize - 1] = '\0';
    }
}

// 格式化文件大小
void FormatFileSize(LARGE_INTEGER fileSize, char *sizeStr, size_t bufferSize)
{
    double size = (double)fileSize.QuadPart;
    const char *units[] = {"B", "KB", "MB", "GB", "TB"};
    int unitIndex = 0;

    while (size >= 1024.0 && unitIndex < 4)
    {
        size /= 1024.0;
        unitIndex++;
    }

    if (unitIndex == 0)
        snprintf(sizeStr, bufferSize, "%.0f %s", size, units[unitIndex]);
    else
        snprintf(sizeStr, bufferSize, "%.1f %s", size, units[unitIndex]);
}

// ============================================================================
// 核心监控功能
// ============================================================================

// 初始化文件监控系统
BOOL InitializeFileWatcher(HWND hwnd)
{
    if (g_watchManager.isInitialized)
        return TRUE;

    memset(&g_watchManager, 0, sizeof(FileWatchManager));
    g_watchManager.mainWindow = hwnd;

    // 获取所有可用驱动器
    DWORD drives = GetLogicalDrives();
    int watcherIndex = 0;

    for (int i = 0; i < 26 && watcherIndex < MAX_DRIVES; i++)
    {
        if (drives & (1 << i))
        {
            WCHAR driveLetter = L'A' + i;

            if (IsNTFSDrive(driveLetter))
            {
                DriveWatcher *watcher = &g_watchManager.watchers[watcherIndex];
                watcher->driveLetter = driveLetter;
                watcher->isActive = FALSE;
                watcher->shouldStop = FALSE;
                watcher->hDirectory = INVALID_HANDLE_VALUE;
                watcher->hThread = NULL;
                watcher->notifyWindow = hwnd;

                watcherIndex++;
            }
        }
    }

    g_watchManager.activeWatcherCount = watcherIndex;
    g_watchManager.isInitialized = TRUE;

    return TRUE;
}

// 启动文件监控
BOOL StartFileWatching()
{
    if (!g_watchManager.isInitialized || g_watchManager.isWatching)
        return FALSE;

    int successCount = 0;
    for (int i = 0; i < g_watchManager.activeWatcherCount; i++)
    {
        if (StartDriveWatcher(&g_watchManager.watchers[i]))
            successCount++;
    }

    if (successCount > 0)
    {
        g_watchManager.isWatching = TRUE;
        return TRUE;
    }

    return FALSE;
}

// 启动单个驱动器监控
BOOL StartDriveWatcher(DriveWatcher *watcher)
{
    if (!watcher || watcher->isActive)
        return FALSE;

    // 打开驱动器根目录
    WCHAR drivePath[4] = {watcher->driveLetter, L':', L'\\', L'\0'};

    watcher->hDirectory = CreateFileW(
        drivePath,
        FILE_LIST_DIRECTORY,
        FILE_SHARE_READ | FILE_SHARE_WRITE | FILE_SHARE_DELETE,
        NULL,
        OPEN_EXISTING,
        FILE_FLAG_BACKUP_SEMANTICS | FILE_FLAG_OVERLAPPED,
        NULL);

    if (watcher->hDirectory == INVALID_HANDLE_VALUE)
        return FALSE;

    // 创建监控线程
    watcher->hThread = CreateThread(NULL, 0, DriveWatcherThread, watcher, 0, NULL);
    if (!watcher->hThread)
    {
        CloseHandle(watcher->hDirectory);
        watcher->hDirectory = INVALID_HANDLE_VALUE;
        return FALSE;
    }

    watcher->isActive = TRUE;
    return TRUE;
}

// 驱动器监控线程
DWORD WINAPI DriveWatcherThread(LPVOID lpParam)
{
    DriveWatcher *watcher = (DriveWatcher *)lpParam;
    DWORD bytesReturned;

    while (!watcher->shouldStop)
    {
        // 初始化重叠结构
        memset(&watcher->overlapped, 0, sizeof(OVERLAPPED));
        watcher->overlapped.hEvent = CreateEvent(NULL, TRUE, FALSE, NULL);

        if (!watcher->overlapped.hEvent)
        {

            break;
        }

        // 开始异步目录监控
        BOOL result = ReadDirectoryChangesW(
            watcher->hDirectory,
            watcher->buffer,
            BUFFER_SIZE,
            TRUE, // 监控子目录
            WATCH_FLAGS,
            &bytesReturned,
            &watcher->overlapped,
            NULL);

        if (!result)
        {
            DWORD error = GetLastError();
            if (error != ERROR_IO_PENDING)
            {

                CloseHandle(watcher->overlapped.hEvent);
                break;
            }
        }

        // 等待变化或停止信号
        DWORD waitResult = WaitForSingleObject(watcher->overlapped.hEvent, 1000);

        if (waitResult == WAIT_OBJECT_0)
        {
            // 获取实际返回的字节数
            if (GetOverlappedResult(watcher->hDirectory, &watcher->overlapped, &bytesReturned, FALSE))
            {
                if (bytesReturned > 0)
                {
                    // 处理文件变化
                    ProcessDirectoryChanges(watcher, watcher->buffer, bytesReturned);
                }
            }
        }
        else if (waitResult == WAIT_TIMEOUT)
        {
            // 超时 - 取消当前操作并继续
            CancelIo(watcher->hDirectory);
        }
        else
        {
            // 错误或停止信号
            CancelIo(watcher->hDirectory);
        }

        CloseHandle(watcher->overlapped.hEvent);

        // 短暂休息避免CPU占用过高
        Sleep(10);
    }

    watcher->isActive = FALSE;
    return 0;
}

// 处理目录变化
void ProcessDirectoryChanges(DriveWatcher *watcher, BYTE *buffer, DWORD bufferSize)
{
    DWORD offset = 0;

    while (offset < bufferSize)
    {
        FILE_NOTIFY_INFORMATION *info = (FILE_NOTIFY_INFORMATION *)(buffer + offset);

        // 创建文件变化事件
        FileChangeEvent event = {0};
        event.action = info->Action;

        // 复制文件名 (转换为null结尾的字符串)
        DWORD nameLength = info->FileNameLength / sizeof(WCHAR);
        if (nameLength >= MAX_PATH)
            nameLength = MAX_PATH - 1;

        wcsncpy(event.fileName, info->FileName, nameLength);
        event.fileName[nameLength] = L'\0';

        // 构建完整路径
        swprintf(event.fullPath, MAX_PATH, L"%c:\\%s",
                 (char)watcher->driveLetter, event.fileName);

        // 获取文件信息 (如果文件存在)
        if (event.action != FILE_ACTION_REMOVED)
        {
            GetFileInfo(event.fullPath, &event.fileSize, &event.lastWriteTime, &event.isDirectory);
        }

        // 处理事件
        ProcessFileChangeEvent(&event);

        // 移动到下一个记录
        if (info->NextEntryOffset == 0)
            break;
        offset += info->NextEntryOffset;
    }
}

// 处理单个文件变化事件
void ProcessFileChangeEvent(const FileChangeEvent *event)
{
    // 跳过目录变化 (只监控文件)
    if (event->isDirectory)
        return;

    char fileName[MAX_PATH];
    char fullPath[MAX_PATH];

    // 转换为UTF-8
    WideCharToMultiByte(CP_UTF8, 0, event->fileName, -1, fileName, sizeof(fileName), NULL, NULL);
    WideCharToMultiByte(CP_UTF8, 0, event->fullPath, -1, fullPath, sizeof(fullPath), NULL, NULL);

    // 过滤掉临时文件和系统文件
    if (strstr(fileName, ".tmp") || strstr(fileName, "~") ||
        strstr(fullPath, "\\System Volume Information") ||
        strstr(fullPath, "\\$Recycle.Bin") ||
        strstr(fullPath, "\\Windows\\Temp"))
    {
        return; // 跳过临时文件和系统文件
    }

    // 限制输出频率 - 只输出重要操作
    static int eventCount = 0;
    eventCount++;

    // 根据操作类型更新数据库
    switch (event->action)
    {
    case FILE_ACTION_ADDED:

        AddFileToIndex(event->fileName, event->fullPath, &event->fileSize, &event->lastWriteTime);
        // 减少UI更新频率
        if (eventCount % 5 == 0 && g_watchManager.mainWindow)
            PostMessage(g_watchManager.mainWindow, WM_FILE_CHANGE_DETECTED, CHANGE_TYPE_ADDED, 0);
        break;

    case FILE_ACTION_REMOVED:

        RemoveFileFromIndex(event->fullPath);
        if (eventCount % 5 == 0 && g_watchManager.mainWindow)
            PostMessage(g_watchManager.mainWindow, WM_FILE_CHANGE_DETECTED, CHANGE_TYPE_REMOVED, 0);
        break;

    case FILE_ACTION_MODIFIED:
        // 修改事件最频繁，进一步限制

        UpdateFileInIndex(event->fullPath, &event->fileSize, &event->lastWriteTime);
        if (eventCount % 10 == 0 && g_watchManager.mainWindow)
            PostMessage(g_watchManager.mainWindow, WM_FILE_CHANGE_DETECTED, CHANGE_TYPE_MODIFIED, 0);
        break;

    case FILE_ACTION_RENAMED_OLD_NAME:
        // 旧文件名 - 从数据库中删除

        RemoveFileFromIndex(event->fullPath);
        break;

    case FILE_ACTION_RENAMED_NEW_NAME:
        // 新文件名 - 添加到数据库

        AddFileToIndex(event->fileName, event->fullPath, &event->fileSize, &event->lastWriteTime);
        if (eventCount % 5 == 0 && g_watchManager.mainWindow)
            PostMessage(g_watchManager.mainWindow, WM_FILE_CHANGE_DETECTED, CHANGE_TYPE_RENAMED, 0);
        break;
    }
}

// ============================================================================
// 数据库操作函数（占位符）
// ============================================================================

// 添加文件到索引（占位符）
BOOL AddFileToIndex(const WCHAR *fileName, const WCHAR *fullPath,
                    const LARGE_INTEGER *fileSize, const FILETIME *lastWriteTime)
{
    // 数据库功能已移除，保留接口兼容性
    return TRUE;
}

// 从索引中删除文件（占位符）
BOOL RemoveFileFromIndex(const WCHAR *fullPath)
{
    // 数据库功能已移除，保留接口兼容性
    return TRUE;
}

// 更新索引中的文件信息（占位符）
BOOL UpdateFileInIndex(const WCHAR *fullPath, const LARGE_INTEGER *fileSize,
                       const FILETIME *lastWriteTime)
{
    // 数据库功能已移除，保留接口兼容性
    return TRUE;
}

// ============================================================================
// 配置管理函数
// ============================================================================

// 检查数据库是否为首次运行
BOOL IsFirstRun()
{
    DWORD fileAttr = GetFileAttributes("file_index.db");
    return (fileAttr == INVALID_FILE_ATTRIBUTES);
}

// 检查配置文件是否为首次运行
BOOL IsConfigFirstRun()
{
    DWORD fileAttr = GetFileAttributes("config.ini");
    return (fileAttr == INVALID_FILE_ATTRIBUTES);
}

// 标记初始扫描完成
void MarkInitialScanComplete()
{
    WritePrivateProfileString("FileWatcher", "InitialScanCompleted", "1", ".\\config.ini");
    WritePrivateProfileString(NULL, NULL, NULL, ".\\config.ini");
}

// 检查扫描是否已完成
BOOL IsScanCompleted()
{
    char scanCompleted[10];
    GetPrivateProfileString("FileWatcher", "InitialScanCompleted", "0",
                            scanCompleted, sizeof(scanCompleted), ".\\config.ini");
    return (strcmp(scanCompleted, "1") == 0);
}

// 检查是否需要进行初始扫描
BOOL ShouldDoInitialScan()
{
    return IsFirstRun() || !IsScanCompleted();
}

// ============================================================================
// 监控控制函数
// ============================================================================

// 停止文件监控
BOOL StopFileWatching()
{
    if (!g_watchManager.isWatching)
        return TRUE;

    for (int i = 0; i < g_watchManager.activeWatcherCount; i++)
    {
        StopDriveWatcher(&g_watchManager.watchers[i]);
    }

    g_watchManager.isWatching = FALSE;

    return TRUE;
}

// 停止单个驱动器监控
BOOL StopDriveWatcher(DriveWatcher *watcher)
{
    if (!watcher || !watcher->isActive)
        return TRUE;

    watcher->shouldStop = TRUE;

    if (watcher->hThread)
    {
        // 等待线程结束
        WaitForSingleObject(watcher->hThread, 5000);
        CloseHandle(watcher->hThread);
        watcher->hThread = NULL;
    }

    if (watcher->hDirectory != INVALID_HANDLE_VALUE)
    {
        CloseHandle(watcher->hDirectory);
        watcher->hDirectory = INVALID_HANDLE_VALUE;
    }

    watcher->isActive = FALSE;
    return TRUE;
}

// 清理文件监控系统
void CleanupFileWatcher()
{
    StopFileWatching();
    memset(&g_watchManager, 0, sizeof(FileWatchManager));
}
