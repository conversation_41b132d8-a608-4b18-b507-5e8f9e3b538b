#ifndef FILE_WATCHER_H
#define FILE_WATCHER_H

#include <windows.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

// 文件监控常量
#define MAX_DRIVES 26
#define BUFFER_SIZE 8192
#define MAX_PATH_LEN 32767

// 文件操作类型 (Windows API定义)
#ifndef FILE_ACTION_ADDED
#define FILE_ACTION_ADDED 0x00000001
#define FILE_ACTION_REMOVED 0x00000002
#define FILE_ACTION_MODIFIED 0x00000003
#define FILE_ACTION_RENAMED_OLD_NAME 0x00000004
#define FILE_ACTION_RENAMED_NEW_NAME 0x00000005
#endif

// 监控标志
#define WATCH_FLAGS (FILE_NOTIFY_CHANGE_FILE_NAME |  \
                     FILE_NOTIFY_CHANGE_DIR_NAME |   \
                     FILE_NOTIFY_CHANGE_SIZE |       \
                     FILE_NOTIFY_CHANGE_LAST_WRITE | \
                     FILE_NOTIFY_CHANGE_CREATION)

// 驱动器监控结构
typedef struct
{
    WCHAR driveLetter;
    HANDLE hDirectory;
    HANDLE hThread;
    OVERLAPPED overlapped;
    BYTE buffer[BUFFER_SIZE];
    BOOL isActive;
    BOOL shouldStop;
    HWND notifyWindow;
} DriveWatcher;

// 文件变化事件结构
typedef struct
{
    DWORD action;
    WCHAR fileName[MAX_PATH];
    WCHAR fullPath[MAX_PATH];
    LARGE_INTEGER fileSize;
    FILETIME lastWriteTime;
    BOOL isDirectory;
} FileChangeEvent;

// 全局监控管理器
typedef struct
{
    DriveWatcher watchers[MAX_DRIVES];
    int activeWatcherCount;
    BOOL isInitialized;
    BOOL isWatching;
    HWND mainWindow;
} FileWatchManager;

// 函数声明

// 初始化和清理
BOOL InitializeFileWatcher(HWND hwnd);
BOOL StartFileWatching();
BOOL StopFileWatching();
void CleanupFileWatcher();

// 驱动器监控
BOOL StartDriveWatcher(DriveWatcher *watcher);
BOOL StopDriveWatcher(DriveWatcher *watcher);
DWORD WINAPI DriveWatcherThread(LPVOID lpParam);

// 事件处理
void ProcessDirectoryChanges(DriveWatcher *watcher, BYTE *buffer, DWORD bufferSize);
void ProcessFileChangeEvent(const FileChangeEvent *event);

// 数据库操作
BOOL AddFileToIndex(const WCHAR *fileName, const WCHAR *fullPath,
                    const LARGE_INTEGER *fileSize, const FILETIME *lastWriteTime);
BOOL RemoveFileFromIndex(const WCHAR *fullPath);
BOOL UpdateFileInIndex(const WCHAR *fullPath, const LARGE_INTEGER *fileSize,
                       const FILETIME *lastWriteTime);

// 工具函数
BOOL GetFileInfo(const WCHAR *filePath, LARGE_INTEGER *fileSize, FILETIME *lastWriteTime, BOOL *isDirectory);
void ConvertFileTimeToString(const FILETIME *fileTime, char *timeString, size_t bufferSize);
BOOL IsNTFSDrive(WCHAR driveLetter);
void FormatFileSize(LARGE_INTEGER fileSize, char *sizeStr, size_t bufferSize);

// 配置管理
BOOL IsFirstRun();              // 检查数据库是否为首次运行
BOOL IsConfigFirstRun();        // 检查配置文件是否为首次运行
BOOL IsScanCompleted();         // 检查扫描是否已完成
void MarkInitialScanComplete(); // 标记初始扫描完成
BOOL ShouldDoInitialScan();     // 检查是否需要进行初始扫描

// 自定义消息
#define WM_FILE_CHANGE_DETECTED (WM_USER + 300)
#define WM_INITIAL_SCAN_COMPLETE (WM_USER + 301)

// 文件变化类型 (用于消息传递)
#define CHANGE_TYPE_ADDED 1
#define CHANGE_TYPE_REMOVED 2
#define CHANGE_TYPE_MODIFIED 3
#define CHANGE_TYPE_RENAMED 4

#endif // FILE_WATCHER_H
