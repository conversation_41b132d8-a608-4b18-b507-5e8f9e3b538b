#ifndef USN_JOURNAL_H
#define USN_JOURNAL_H

#include <windows.h>
#include <winioctl.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>

// Forward declaration for SQLite3
typedef struct sqlite3 sqlite3;
typedef struct sqlite3_stmt sqlite3_stmt;

// USN Journal structures and constants
#define USN_BUFFER_SIZE 0x10000000 // 64MB buffer
#define MAX_PATH_LENGTH 32767      // Maximum path length in Windows

// USN Journal structures (if not defined in winioctl.h)
#ifndef USN_RECORD_V2_DEFINED
#define USN_RECORD_V2_DEFINED

typedef struct
{
    DWORD RecordLength;
    WORD MajorVersion;
    WORD MinorVersion;
    DWORDLONG FileReferenceNumber;
    DWORDLONG ParentFileReferenceNumber;
    USN Usn;
    LARGE_INTEGER TimeStamp;
    DWORD Reason;
    DWORD SourceInfo;
    DWORD SecurityId;
    DWORD FileAttributes;
    WORD FileNameLength;
    WORD FileNameOffset;
    WCHAR FileName[1];
} USN_RECORD_V2, *PUSN_RECORD_V2;

// USN Journal Data structures (only define if not already defined)
#ifndef USN_JOURNAL_DATA_V0_DEFINED
#define USN_JOURNAL_DATA_V0_DEFINED
typedef struct
{
    DWORDLONG UsnJournalID;
    USN FirstUsn;
    USN NextUsn;
    USN LowestValidUsn;
    USN MaxUsn;
    DWORDLONG MaximumSize;
    DWORDLONG AllocationDelta;
} USN_JOURNAL_DATA_V0, *PUSN_JOURNAL_DATA_V0;
#endif

// Note: CREATE_USN_JOURNAL_DATA, DELETE_USN_JOURNAL_DATA, and MFT_ENUM_DATA
// are already defined in winioctl.h in newer MinGW versions

// USN Delete flags
#ifndef USN_DELETE_FLAG_DELETE
#define USN_DELETE_FLAG_DELETE 0x00000001
#endif

#endif

// File information structure
typedef struct
{
    DWORDLONG fileReferenceNumber;
    DWORDLONG parentFileReferenceNumber;
    WCHAR fileName[MAX_PATH];
    DWORD fileAttributes;
    LARGE_INTEGER fileSize;
    LARGE_INTEGER lastWriteTime;
} FileInfo;

// Path reconstruction structure
typedef struct
{
    DWORDLONG frn;
    DWORDLONG parentFrn;
    WCHAR fileName[MAX_PATH];
} FrnEntry;

// Volume processing class
typedef struct
{
    HANDLE volumeHandle;
    WCHAR volumeLetter;
    USN_JOURNAL_DATA_V0 journalData;
    CREATE_USN_JOURNAL_DATA createData;
    DELETE_USN_JOURNAL_DATA deleteData;

    // USN Journal monitoring fields
    USN nextUsn;         // Next USN to read for real-time monitoring
    DWORDLONG journalId; // Journal ID for validation

    // Hash table for FRN to name mapping (simplified)
    FrnEntry *frnMap;
    DWORD frnMapSize;
    DWORD frnMapCapacity;
} VolumeProcessor;

// Database functions
int InitializeDatabase(const char *dbPath);
int InsertFileRecord(const char *name, const char *path, LONGLONG size, const char *modified);
int UpdateFileRecord(const char *path, LONGLONG size, const char *modified);
int DeleteFileRecord(const char *path);
int ClearDatabase();
void CloseDatabase();
int CompressDatabase();

// USN Journal functions
BOOL OpenVolume(VolumeProcessor *processor, WCHAR driveLetter);
BOOL CreateUSNJournal(VolumeProcessor *processor);
BOOL QueryUSNJournal(VolumeProcessor *processor);
BOOL EnumerateUSNData(VolumeProcessor *processor);
BOOL DeleteUSNJournal(VolumeProcessor *processor);
void CloseVolume(VolumeProcessor *processor);

// Path reconstruction functions
void AddFrnEntry(VolumeProcessor *processor, DWORDLONG frn, DWORDLONG parentFrn, const WCHAR *fileName);
BOOL GetFullPath(VolumeProcessor *processor, DWORDLONG frn, WCHAR *fullPath, DWORD pathSize);

// Utility functions
void FileTimeToString(const LARGE_INTEGER *fileTime, char *timeString, size_t bufferSize);
void ConvertFileTimeToString(const FILETIME *fileTime, char *buffer, size_t bufferSize);
LONGLONG FileSizeToLongLong(const LARGE_INTEGER *fileSize);
void WCharToChar(const WCHAR *wstr, char *str, size_t bufferSize);

// USN Journal real-time monitoring functions
BOOL ProcessSingleUSNRecord(VolumeProcessor *processor, PUSN_RECORD_V2 usnRecord, HWND mainWindow);
BOOL ProcessUSNJournalChanges(VolumeProcessor *processor, HWND mainWindow);

// Thread function for USN processing
DWORD WINAPI USNProcessingThread(LPVOID lpParam);

// Main processing function
BOOL ProcessAllVolumes();

// Full disk scan function
int ScanAllDrives();

// Statistics and reporting
void ReportScanStatistics();
BOOL VerifyScanSuccess();

// USN file counting functions
int GetUSNFileCount();
int CountUSNFilesOnDrive(WCHAR driveLetter);

// Fallback scanning functions
int ScanDirectorySimple(const char *dirPath);
int ScanDirectoryRecursive(const char *dirPath, int depth);

// File processing functions
BOOL ProcessFilesToDatabase(VolumeProcessor *processor);

// 简化的USN读取函数
int ReadUSNToMemory();

// USN Journal扫描函数
int ScanDriveUSN(const WCHAR *drivePath);
int EnumerateUSNRecords(HANDLE hVolume, const WCHAR *drivePath);
void ProcessUSNRecord(PUSN_RECORD_V2 usnRecord, const WCHAR *drivePath);
int FallbackDirectoryScan(const WCHAR *drivePath);
int SimpleDirectoryScan(const WCHAR *drivePath);
int ScanDirectoryWithDepth(const WCHAR *dirPath, int *totalCount, int maxFiles, int currentDepth, int maxDepth);

// USN内存管理函数（在main.c中实现）
void AddUSNFile(const WCHAR *fileName, const WCHAR *fullPath, LONGLONG fileSize, FILETIME lastWriteTime, DWORD fileAttributes);

#endif // USN_JOURNAL_H
