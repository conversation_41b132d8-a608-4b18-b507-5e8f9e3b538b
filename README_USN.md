# USN Journal File Indexer

基于Master File Table (MTF)分析实现的USN Journal文件索引器，可以快速索引和搜索NTFS磁盘上的所有文件。

## 功能特性

- **USN Journal索引**: 使用Windows USN Journal技术快速扫描NTFS磁盘
- **多线程处理**: 并行处理多个磁盘驱动器
- **SQLite数据库**: 高效存储和查询文件索引
- **实时搜索**: 在搜索框中输入关键词即时过滤结果
- **文件信息显示**: 显示文件名、路径、大小、修改时间
- **可调整界面**: 支持分割线拖拽调整窗口布局

## 系统要求

- Windows 7/8/10/11 (需要NTFS文件系统)
- 管理员权限 (USN Journal访问需要)
- SQLite3开发库

## 编译说明

### 1. 下载SQLite3库

从 https://www.sqlite.org/download.html 下载以下文件：
- `sqlite3.h` - 头文件
- `sqlite3.lib` - 静态库文件 (Windows)

将这些文件放在项目目录中。

### 2. 编译程序

运行编译脚本：
```batch
build.bat
```

或手动编译：
```batch
gcc -c usn_journal.c -o usn_journal.o -std=c99 -Wall
gcc -o main.exe main.c usn_journal.o -lgdi32 -lcomctl32 -luser32 -lsqlite3 -L. -std=c99 -Wall
```

## 使用方法

### 1. 启动程序
```batch
# 以管理员身份运行
main.exe
```

### 2. 自动索引
程序启动后会自动：
- 检测所有NTFS驱动器
- 创建USN Journal (如果不存在)
- 扫描所有文件并建立索引
- 将结果存储到SQLite数据库

### 3. 搜索文件
- 在顶部搜索框中输入关键词
- 程序会实时过滤显示匹配的文件
- 支持文件名和路径搜索

### 4. 查看结果
文件列表显示四列信息：
- **Name**: 文件名（含扩展名）
- **Path**: 完整文件路径
- **Size**: 文件大小（自动格式化为KB/MB/GB）
- **Modified**: 文件修改时间

## 技术实现

### USN Journal技术
基于Master File Table (MTF).txt中的方法：
- 使用`FSCTL_CREATE_USN_JOURNAL`创建USN Journal
- 通过`FSCTL_ENUM_USN_DATA`枚举所有文件记录
- 构建FRN (File Reference Number)到文件名的映射
- 重建完整文件路径

### 数据库结构
```sql
CREATE TABLE files (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    path TEXT NOT NULL UNIQUE,
    size INTEGER,
    modified TEXT,
    indexed_time DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 多线程架构
- 主线程: UI界面和用户交互
- 索引线程: 后台处理USN Journal扫描
- 每个驱动器: 独立线程处理

## 文件说明

- `main.c` - 主程序和UI界面
- `usn_journal.h` - USN Journal处理头文件
- `usn_journal.c` - USN Journal处理实现
- `build.bat` - 编译脚本
- `file_index.db` - SQLite数据库文件（自动生成）
- `config.ini` - 窗口配置文件（自动生成）

## 注意事项

1. **管理员权限**: 访问USN Journal需要管理员权限
2. **NTFS限制**: 只支持NTFS文件系统的驱动器
3. **首次扫描**: 首次运行时扫描可能需要较长时间
4. **内存使用**: 大量文件时会占用较多内存
5. **数据库大小**: 数据库文件大小取决于文件数量

## 故障排除

### 编译错误
- 确保SQLite3库文件在正确位置
- 检查编译器是否支持C99标准

### 运行错误
- 确保以管理员身份运行
- 检查目标驱动器是否为NTFS格式
- 确保有足够的磁盘空间存储数据库

### 性能问题
- 首次扫描时间较长是正常现象
- 可以通过状态栏查看进度
- 大型驱动器建议有足够内存

## 许可证

本项目基于MIT许可证开源。
