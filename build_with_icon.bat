@echo off
echo 编译Everyview64 带图标版本...

echo 第一步：编译资源文件...
windres resource.rc -o resource.o

if %errorlevel% neq 0 (
    echo 资源文件编译失败！
    pause
    exit /b 1
)

echo 第二步：编译主程序并链接资源...
gcc -mwindows -o main.exe main.c file_watcher.c usn_journal.c resource.o -lcomctl32 -luser32 -lgdi32 -lkernel32 -ladvapi32

if %errorlevel% == 0 (
    echo 编译成功！
    echo 程序已编译为GUI版本，包含自定义图标
    echo 图标文件：resource/icons8_32.ico
) else (
    echo 编译失败！
)

echo 清理临时文件...
if exist resource.o del resource.o

pause
