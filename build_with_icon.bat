@echo off
echo 编译Everyview64 带图标版本...

echo 第一步：编译资源文件...
windres resource.rc -o resource.o

if %errorlevel% neq 0 (
    echo 资源文件编译失败！
    pause
    exit /b 1
)

echo 第二步：编译主程序并链接资源...
gcc -O2 -mwindows -o EveryView.exe main.c file_watcher.c usn_journal.c resource.o -lcomctl32 -luser32 -lgdi32 -lkernel32 -ladvapi32 -lshell32

if %errorlevel% == 0 (
    echo 编译成功！生成了 EveryView.exe
    echo 程序已编译为GUI版本，包含自定义图标
    echo 图标文件：resource/icons8_32.ico
) else (
    echo 编译失败！
    echo 尝试不带图标编译...
    gcc -O2 -mwindows -o EveryView.exe main.c file_watcher.c usn_journal.c -lcomctl32 -luser32 -lgdi32 -lkernel32 -ladvapi32 -lshell32
    if %errorlevel% == 0 (
        echo 不带图标编译成功！生成了 EveryView.exe
    ) else (
        echo 编译完全失败！
    )
)

echo 清理临时文件...
if exist resource.o del resource.o

pause
